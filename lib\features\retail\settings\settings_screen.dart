import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'settings_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';

/// Settings screen for app configuration
class SettingsScreen extends GetView<SettingsController> {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Language settings
                _buildLanguageSection(),
                
                // Theme settings
                _buildThemeSection(),
                
                // Notification settings
                _buildNotificationSection(),
                
                // App settings
                _buildAppSection(),
                
                // About section
                _buildAboutSection(),
                
                SizedBox(height: 20.h),
              ],
            ),
          );
        }),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('settings'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
    );
  }

  Widget _buildLanguageSection() {
    return _buildSection(
      title: 'language_settings'.tr,
      icon: Icons.language,
      children: [
        Obx(() => _buildLanguageSelector()),
      ],
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: controller.selectedLanguage.value,
          isExpanded: true,
          items: controller.languageOptions.map((language) {
            return DropdownMenuItem(
              value: language['code'],
              child: Row(
                children: [
                  Text(
                    language['flag']!,
                    style: TextStyle(fontSize: 20.sp),
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    language['name']!,
                    style: AppTextStyles.bodyMedium,
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              controller.changeLanguage(value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildThemeSection() {
    return _buildSection(
      title: 'theme_settings'.tr,
      icon: Icons.palette,
      children: [
        Obx(() => _buildSwitchTile(
          title: 'dark_mode'.tr,
          subtitle: 'enable_dark_theme'.tr,
          value: controller.isDarkMode.value,
          onChanged: controller.toggleDarkMode,
          icon: Icons.dark_mode,
        )),
      ],
    );
  }

  Widget _buildNotificationSection() {
    return _buildSection(
      title: 'notification_settings'.tr,
      icon: Icons.notifications,
      children: [
        Obx(() => _buildSwitchTile(
          title: 'enable_notifications'.tr,
          subtitle: 'receive_app_notifications'.tr,
          value: controller.notificationsEnabled.value,
          onChanged: controller.toggleNotifications,
          icon: Icons.notifications_active,
        )),
        
        if (controller.notificationsEnabled.value) ...[
          SizedBox(height: 8.h),
          Obx(() => _buildSwitchTile(
            title: 'order_notifications'.tr,
            subtitle: 'notifications_for_orders'.tr,
            value: controller.orderNotifications.value,
            onChanged: controller.toggleOrderNotifications,
            icon: Icons.shopping_cart,
            isSubSetting: true,
          )),
          
          Obx(() => _buildSwitchTile(
            title: 'visit_notifications'.tr,
            subtitle: 'notifications_for_visits'.tr,
            value: controller.visitNotifications.value,
            onChanged: controller.toggleVisitNotifications,
            icon: Icons.event,
            isSubSetting: true,
          )),
          
          Obx(() => _buildSwitchTile(
            title: 'payment_notifications'.tr,
            subtitle: 'notifications_for_payments'.tr,
            value: controller.paymentNotifications.value,
            onChanged: controller.togglePaymentNotifications,
            icon: Icons.payment,
            isSubSetting: true,
          )),
          
          Obx(() => _buildSwitchTile(
            title: 'system_notifications'.tr,
            subtitle: 'notifications_for_system_updates'.tr,
            value: controller.systemNotifications.value,
            onChanged: controller.toggleSystemNotifications,
            icon: Icons.system_update,
            isSubSetting: true,
          )),
          
          SizedBox(height: 8.h),
          Obx(() => _buildSwitchTile(
            title: 'sound'.tr,
            subtitle: 'enable_notification_sound'.tr,
            value: controller.soundEnabled.value,
            onChanged: controller.toggleSound,
            icon: Icons.volume_up,
            isSubSetting: true,
          )),
          
          Obx(() => _buildSwitchTile(
            title: 'vibration'.tr,
            subtitle: 'enable_notification_vibration'.tr,
            value: controller.vibrationEnabled.value,
            onChanged: controller.toggleVibration,
            icon: Icons.vibration,
            isSubSetting: true,
          )),
        ],
      ],
    );
  }

  Widget _buildAppSection() {
    return _buildSection(
      title: 'app_settings'.tr,
      icon: Icons.settings,
      children: [
        _buildActionTile(
          title: 'clear_cache'.tr,
          subtitle: 'free_up_storage_space'.tr,
          icon: Icons.cleaning_services,
          onTap: controller.clearCache,
        ),
        _buildActionTile(
          title: 'reset_settings'.tr,
          subtitle: 'restore_default_settings'.tr,
          icon: Icons.restore,
          onTap: controller.resetSettings,
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return _buildSection(
      title: 'about'.tr,
      icon: Icons.info,
      children: [
        _buildInfoTile(
          title: 'app_version'.tr,
          value: controller.appVersion,
          icon: Icons.info,
        ),
        _buildInfoTile(
          title: 'build_number'.tr,
          value: controller.buildNumber,
          icon: Icons.build,
        ),
        _buildActionTile(
          title: 'privacy_policy'.tr,
          subtitle: 'read_privacy_policy'.tr,
          icon: Icons.privacy_tip,
          onTap: controller.navigateToPrivacyPolicy,
        ),
        _buildActionTile(
          title: 'terms_of_service'.tr,
          subtitle: 'read_terms_of_service'.tr,
          icon: Icons.description,
          onTap: controller.navigateToTermsOfService,
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
    bool isSubSetting = false,
  }) {
    return Container(
      margin: EdgeInsets.only(
        right: isSubSetting ? 24.w : 0,
        bottom: 8.h,
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSubSetting ? AppColors.onSurfaceVariant : AppColors.primary,
            size: 18.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.onSurface,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: 18.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.onSurface,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 14.sp,
              color: AppColors.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: 18.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
