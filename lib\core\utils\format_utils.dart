import 'package:intl/intl.dart';

/// Formatting utilities for Al Ameen Sales App
class FormatUtils {
  // Prevent instantiation
  FormatUtils._();

  /// Format currency with symbol
  static String formatCurrency(double amount, {String currency = 'USD', String locale = 'en_US'}) {
    final formatter = NumberFormat.currency(locale: locale, symbol: '\$', decimalDigits: 2);
    return formatter.format(amount);
  }

  /// Format currency without symbol
  static String formatAmount(double amount, {int decimalPlaces = 2}) {
    final formatter = NumberFormat('#,##0.${'0' * decimalPlaces}');
    return formatter.format(amount);
  }

  /// Format Saudi currency (SAR)
  static String formatSaudiCurrency(double amount, {bool showSymbol = true}) {
    final formatter = NumberFormat('#,##0.00');
    final formattedAmount = formatter.format(amount);
    return showSymbol ? '$formattedAmount ر.س' : formattedAmount;
  }

  /// Format percentage
  static String formatPercentage(double value, {int decimalPlaces = 1}) {
    final formatter = NumberFormat('#,##0.${'0' * decimalPlaces}%');
    return formatter.format(value * 100);
  }

  /// Format number with commas
  static String formatNumber(num number, {int decimalPlaces = 0}) {
    final formatter = NumberFormat('#,##0.${'0' * decimalPlaces}');
    return formatter.format(number);
  }

  /// Format phone number
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');

    if (digits.length == 10) {
      // Format as (XXX) XXX-XXXX
      return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
    } else if (digits.length == 11 && digits.startsWith('1')) {
      // Format as +1 (XXX) XXX-XXXX
      return '+1 (${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}';
    } else {
      // Return original if format is not recognized
      return phoneNumber;
    }
  }

  /// Format Saudi phone number
  static String formatSaudiPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');

    if (digits.length == 9 && digits.startsWith('5')) {
      // Format as 05X XXX XXXX
      return '05${digits.substring(1, 2)} ${digits.substring(2, 5)} ${digits.substring(5)}';
    } else if (digits.length == 10 && digits.startsWith('05')) {
      // Format as 05X XXX XXXX
      return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
    } else if (digits.length == 12 && digits.startsWith('966')) {
      // Format as +966 5X XXX XXXX
      return '+966 ${digits.substring(3, 5)} ${digits.substring(5, 8)} ${digits.substring(8)}';
    } else {
      // Return original if format is not recognized
      return phoneNumber;
    }
  }

  /// Format file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Format date in various formats
  static String formatDate(DateTime date, {String format = 'yyyy-MM-dd'}) {
    final formatter = DateFormat(format);
    return formatter.format(date);
  }

  /// Format date and time
  static String formatDateTime(DateTime dateTime, {String format = 'yyyy-MM-dd HH:mm'}) {
    final formatter = DateFormat(format);
    return formatter.format(dateTime);
  }

  /// Format date in Arabic locale
  static String formatDateArabic(DateTime date, {String format = 'dd/MM/yyyy'}) {
    final formatter = DateFormat(format, 'ar');
    return formatter.format(date);
  }

  /// Format time only
  static String formatTime(DateTime time, {bool use24Hour = false}) {
    final format = use24Hour ? 'HH:mm' : 'hh:mm a';
    final formatter = DateFormat(format);
    return formatter.format(time);
  }

  /// Format relative time (e.g., "2 hours ago")
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return formatDate(dateTime, format: 'MMM dd, yyyy');
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  /// Capitalize first letter of each word
  static String capitalizeWords(String text) {
    if (text.isEmpty) return text;
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Capitalize first letter only
  static String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Convert to title case
  static String toTitleCase(String text) {
    return capitalizeWords(text);
  }

  /// Convert camelCase to readable text
  static String camelCaseToReadable(String camelCase) {
    return camelCase
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .trim()
        .toLowerCase()
        .split(' ')
        .map((word) => capitalizeFirst(word))
        .join(' ');
  }

  /// Convert snake_case to readable text
  static String snakeCaseToReadable(String snakeCase) {
    return snakeCase
        .split('_')
        .map((word) => capitalizeFirst(word))
        .join(' ');
  }

  /// Truncate text with ellipsis
  static String truncateText(String text, int maxLength, {String ellipsis = '...'}) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - ellipsis.length)}$ellipsis';
  }

  /// Get initials from full name
  static String getInitials(String fullName, {int maxInitials = 2}) {
    final words = fullName.trim().split(RegExp(r'\s+'));
    final initials = words
        .take(maxInitials)
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
        .where((initial) => initial.isNotEmpty)
        .join();
    return initials;
  }

  /// Format order number with prefix
  static String formatOrderNumber(int orderNumber, {String prefix = 'ORD'}) {
    return '$prefix-${orderNumber.toString().padLeft(6, '0')}';
  }

  /// Format invoice number with prefix
  static String formatInvoiceNumber(int invoiceNumber, {String prefix = 'INV'}) {
    return '$prefix-${invoiceNumber.toString().padLeft(6, '0')}';
  }

  /// Format customer code with prefix
  static String formatCustomerCode(int customerNumber, {String prefix = 'CUST'}) {
    return '$prefix-${customerNumber.toString().padLeft(4, '0')}';
  }

  /// Format return number with prefix
  static String formatReturnNumber(int returnNumber, {String prefix = 'RET'}) {
    return '$prefix-${returnNumber.toString().padLeft(6, '0')}';
  }

  /// Format visit reference with prefix
  static String formatVisitReference(int visitNumber, {String prefix = 'VIS'}) {
    return '$prefix-${visitNumber.toString().padLeft(6, '0')}';
  }

  /// Format status text for display
  static String formatStatus(String status) {
    return status
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => capitalizeFirst(word))
        .join(' ');
  }

  /// Format address for display
  static String formatAddress({
    String? street,
    String? city,
    String? state,
    String? zipCode,
    String? country,
  }) {
    final parts = <String>[];
    
    if (street?.isNotEmpty == true) parts.add(street!);
    if (city?.isNotEmpty == true) parts.add(city!);
    if (state?.isNotEmpty == true) parts.add(state!);
    if (zipCode?.isNotEmpty == true) parts.add(zipCode!);
    if (country?.isNotEmpty == true) parts.add(country!);
    
    return parts.join(', ');
  }

  /// Format duration in human readable format
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Format quantity with unit
  static String formatQuantity(double quantity, {String unit = 'pcs'}) {
    if (quantity == quantity.toInt()) {
      return '${quantity.toInt()} $unit';
    } else {
      return '${quantity.toStringAsFixed(2)} $unit';
    }
  }

  /// Format discount
  static String formatDiscount(double discount, {bool isPercentage = true}) {
    if (isPercentage) {
      return '${discount.toStringAsFixed(1)}%';
    } else {
      return formatCurrency(discount);
    }
  }

  /// Format tax rate
  static String formatTaxRate(double taxRate) {
    return '${taxRate.toStringAsFixed(2)}%';
  }

  /// Remove special characters and spaces
  static String sanitizeString(String input) {
    return input.replaceAll(RegExp(r'[^\w\s]'), '').replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  /// Format search query
  static String formatSearchQuery(String query) {
    return sanitizeString(query).toLowerCase();
  }

  /// Format email for display (mask middle part)
  static String maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 3) return email;
    
    final maskedUsername = '${username.substring(0, 2)}${'*' * (username.length - 3)}${username.substring(username.length - 1)}';
    return '$maskedUsername@$domain';
  }

  /// Format phone for display (mask middle part)
  static String maskPhone(String phone) {
    final digits = phone.replaceAll(RegExp(r'\D'), '');
    if (digits.length < 10) return phone;
    
    final start = digits.substring(0, 3);
    final end = digits.substring(digits.length - 4);
    final masked = '$start***$end';
    
    return formatPhoneNumber(masked);
  }

  /// Format credit card number (mask middle digits)
  static String maskCreditCard(String cardNumber) {
    final digits = cardNumber.replaceAll(RegExp(r'\D'), '');
    if (digits.length < 12) return cardNumber;
    
    final start = digits.substring(0, 4);
    final end = digits.substring(digits.length - 4);
    return '$start **** **** $end';
  }

  /// Convert string to slug (URL-friendly)
  static String toSlug(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .replaceAll(RegExp(r'-+'), '-')
        .replaceAll(RegExp(r'^-|-$'), '');
  }

  /// Format list to readable string
  static String formatList(List<String> items, {String separator = ', ', String lastSeparator = ' and '}) {
    if (items.isEmpty) return '';
    if (items.length == 1) return items.first;
    if (items.length == 2) return '${items.first}$lastSeparator${items.last}';
    
    final allButLast = items.sublist(0, items.length - 1).join(separator);
    return '$allButLast$lastSeparator${items.last}';
  }

  /// Format boolean to Yes/No
  static String formatBoolean(bool value, {String trueText = 'Yes', String falseText = 'No'}) {
    return value ? trueText : falseText;
  }

  /// Format nullable string with fallback
  static String formatNullable(String? value, {String fallback = 'N/A'}) {
    return value?.isNotEmpty == true ? value! : fallback;
  }
}
