import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/// User model for Al Ameen Sales App
@JsonSerializable()
class User {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? avatar;
  final String role; // 'retail' or 'wholesale'
  final String status; // 'active', 'inactive', 'suspended'
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final UserProfile? profile;
  final UserSettings? settings;

  const User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.avatar,
    required this.role,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.profile,
    this.settings,
  });

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get name (alias for fullName for compatibility)
  String get name => fullName;

  /// Get avatar URL (alias for avatar for compatibility)
  String? get avatarUrl => avatar;

  /// Get address from profile
  String? get address => profile?.address;

  /// Get department from profile
  String? get department => profile?.department;

  /// Get initials
  String get initials {
    final first = firstName.isNotEmpty ? firstName[0] : '';
    final last = lastName.isNotEmpty ? lastName[0] : '';
    return '$first$last'.toUpperCase();
  }

  /// Check if user is retail representative
  bool get isRetail => role == 'retail';

  /// Check if user is wholesale representative
  bool get isWholesale => role == 'wholesale';

  /// Check if user is active
  bool get isActive => status == 'active';

  /// Factory constructor for creating User from JSON
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Convert User to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// Create a copy of User with updated fields
  User copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? avatar,
    String? role,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    UserProfile? profile,
    UserSettings? settings,
    // Convenience parameters for compatibility
    String? name,
    String? address,
  }) {
    // Handle convenience parameters
    UserProfile? updatedProfile = profile ?? this.profile;
    if (name != null) {
      final nameParts = name.split(' ');
      final newFirstName = nameParts.isNotEmpty ? nameParts.first : firstName ?? this.firstName;
      final newLastName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : lastName ?? this.lastName;
      return copyWith(
        firstName: newFirstName,
        lastName: newLastName,
        profile: updatedProfile,
      );
    }
    if (address != null) {
      updatedProfile = (this.profile ?? const UserProfile()).copyWith(address: address);
    }

    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      profile: updatedProfile,
      settings: settings ?? this.settings,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'User(id: $id, email: $email, fullName: $fullName, role: $role)';
}

/// User profile information
@JsonSerializable()
class UserProfile {
  final String? department;
  final String? position;
  final String? manager;
  final String? territory;
  final String? region;
  final DateTime? hireDate;
  final String? employeeId;
  final String? address;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;
  final String? bio;

  const UserProfile({
    this.department,
    this.position,
    this.manager,
    this.territory,
    this.region,
    this.hireDate,
    this.employeeId,
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.country,
    this.bio,
  });

  /// Get full address
  String get fullAddress {
    final parts = <String>[];
    if (address?.isNotEmpty == true) parts.add(address!);
    if (city?.isNotEmpty == true) parts.add(city!);
    if (state?.isNotEmpty == true) parts.add(state!);
    if (zipCode?.isNotEmpty == true) parts.add(zipCode!);
    if (country?.isNotEmpty == true) parts.add(country!);
    return parts.join(', ');
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? department,
    String? position,
    String? manager,
    String? territory,
    String? region,
    DateTime? hireDate,
    String? employeeId,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? bio,
  }) {
    return UserProfile(
      department: department ?? this.department,
      position: position ?? this.position,
      manager: manager ?? this.manager,
      territory: territory ?? this.territory,
      region: region ?? this.region,
      hireDate: hireDate ?? this.hireDate,
      employeeId: employeeId ?? this.employeeId,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      bio: bio ?? this.bio,
    );
  }
}

/// User settings and preferences
@JsonSerializable()
class UserSettings {
  final String theme; // 'light', 'dark', 'system'
  final String language; // 'en', 'ar'
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool pushNotifications;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String currency; // 'USD', 'EUR', etc.
  final String dateFormat; // 'dd/MM/yyyy', 'MM/dd/yyyy', etc.
  final String timeFormat; // '24h', '12h'
  final bool autoSync;
  final int syncInterval; // in minutes
  final bool biometricAuth;
  final bool rememberLogin;

  const UserSettings({
    this.theme = 'system',
    this.language = 'en',
    this.notificationsEnabled = true,
    this.emailNotifications = true,
    this.pushNotifications = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.currency = 'USD',
    this.dateFormat = 'dd/MM/yyyy',
    this.timeFormat = '24h',
    this.autoSync = true,
    this.syncInterval = 15,
    this.biometricAuth = false,
    this.rememberLogin = false,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  UserSettings copyWith({
    String? theme,
    String? language,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? pushNotifications,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? currency,
    String? dateFormat,
    String? timeFormat,
    bool? autoSync,
    int? syncInterval,
    bool? biometricAuth,
    bool? rememberLogin,
  }) {
    return UserSettings(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      currency: currency ?? this.currency,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      autoSync: autoSync ?? this.autoSync,
      syncInterval: syncInterval ?? this.syncInterval,
      biometricAuth: biometricAuth ?? this.biometricAuth,
      rememberLogin: rememberLogin ?? this.rememberLogin,
    );
  }
}
