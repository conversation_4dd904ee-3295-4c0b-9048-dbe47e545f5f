import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/notification.dart' as models;
import '../../../core/utils/helpers.dart';

/// Controller for notifications management
class NotificationsController extends GetxController {
  // Observable variables
  final isLoading = false.obs;
  final notifications = <models.Notification>[].obs;
  final filteredNotifications = <models.Notification>[].obs;
  final searchQuery = ''.obs;
  final selectedType = 'all'.obs;
  final selectedStatus = 'all'.obs;
  final selectedDateRange = 'all'.obs;

  // Filter options
  final typeOptions = [
    'all',
    'order_confirmation',
    'payment_reminder',
    'visit_schedule',
    'system_alert',
    'promotion',
    'announcement'
  ].obs;

  final statusOptions = [
    'all',
    'unread',
    'read'
  ].obs;

  final dateRangeOptions = [
    'all',
    'today',
    'this_week',
    'this_month'
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedType, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedDateRange, (_) => _applyFilters());
  }

  /// Load notifications from service
  Future<void> loadNotifications() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      notifications.value = _generateMockNotifications();
      
      // TODO: Replace with actual API call
      // notifications.value = await _notificationService.getNotifications();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_notifications'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = notifications.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((notification) {
        return notification.title.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               notification.message.toLowerCase().contains(searchQuery.value.toLowerCase());
      }).toList();
    }

    // Apply type filter
    if (selectedType.value != 'all') {
      filtered = filtered.where((notification) => notification.type == selectedType.value).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      if (selectedStatus.value == 'read') {
        filtered = filtered.where((notification) => notification.isRead).toList();
      } else if (selectedStatus.value == 'unread') {
        filtered = filtered.where((notification) => !notification.isRead).toList();
      }
    }

    // Apply date range filter
    if (selectedDateRange.value != 'all') {
      final now = DateTime.now();
      DateTime? startDate;

      switch (selectedDateRange.value) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case 'this_week':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          break;
        case 'this_month':
          startDate = DateTime(now.year, now.month, 1);
          break;
      }

      if (startDate != null) {
        filtered = filtered.where((notification) => notification.createdAt.isAfter(startDate!)).toList();
      }
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    filteredNotifications.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update type filter
  void updateTypeFilter(String type) {
    selectedType.value = type;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update date range filter
  void updateDateRangeFilter(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedType.value = 'all';
    selectedStatus.value = 'all';
    selectedDateRange.value = 'all';
  }

  /// Navigate to notification details
  void navigateToNotificationDetails(String notificationId) {
    Get.toNamed('/retail/notifications/$notificationId');
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      // TODO: Call API to mark as read
      // await _notificationService.markAsRead(notificationId);
      
      // Update local list
      final notificationIndex = notifications.indexWhere((notification) => notification.id == notificationId);
      if (notificationIndex != -1) {
        notifications[notificationIndex] = notifications[notificationIndex].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        _applyFilters();
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_mark_as_read'.tr);
    }
  }

  /// Mark notification as unread
  Future<void> markAsUnread(String notificationId) async {
    try {
      // TODO: Call API to mark as unread
      // await _notificationService.markAsUnread(notificationId);
      
      // Update local list
      final notificationIndex = notifications.indexWhere((notification) => notification.id == notificationId);
      if (notificationIndex != -1) {
        notifications[notificationIndex] = notifications[notificationIndex].copyWith(
          isRead: false,
          readAt: null,
        );
        _applyFilters();
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_mark_as_unread'.tr);
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_delete'.tr),
          content: Text('confirm_delete_notification'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('no'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('yes'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to delete notification
        // await _notificationService.deleteNotification(notificationId);
        
        // Update local list
        notifications.removeWhere((notification) => notification.id == notificationId);
        _applyFilters();
        
        Helpers.showSuccessSnackbar('notification_deleted_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_delete_notification'.tr);
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      // TODO: Call API to mark all as read
      // await _notificationService.markAllAsRead();
      
      // Update local list
      for (int i = 0; i < notifications.length; i++) {
        if (!notifications[i].isRead) {
          notifications[i] = notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
      _applyFilters();
      
      Helpers.showSuccessSnackbar('all_notifications_marked_as_read'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_mark_all_as_read'.tr);
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  /// Get unread notifications count
  int get unreadCount {
    return notifications.where((notification) => !notification.isRead).length;
  }

  /// Get total notifications count
  int get totalCount {
    return notifications.length;
  }

  /// Generate mock notifications for demonstration
  List<models.Notification> _generateMockNotifications() {
    final now = DateTime.now();
    return [
      models.Notification(
        id: '1',
        title: 'طلب جديد تم تأكيده',
        message: 'تم تأكيد الطلب رقم ORD-001 من العميل محمد أحمد التجاري',
        type: 'order',
        entityId: 'ORD-001',
        entityType: 'order',
        priority: 'medium',
        isRead: false,
        createdAt: now.subtract(const Duration(minutes: 30)),
        data: {'orderId': 'ORD-001', 'customerId': 'CUST-001'},
        icon: 'shopping_cart',
      ),
      models.Notification(
        id: '2',
        title: 'تذكير بالدفع',
        message: 'الفاتورة INV-002 مستحقة الدفع منذ 3 أيام',
        type: 'reminder',
        entityId: 'INV-002',
        entityType: 'invoice',
        priority: 'high',
        isRead: false,
        createdAt: now.subtract(const Duration(hours: 2)),
        data: {'invoiceId': 'INV-002'},
        icon: 'payment',
      ),
      models.Notification(
        id: '3',
        title: 'زيارة مجدولة',
        message: 'لديك زيارة مجدولة مع العميل فاطمة علي للتجارة في الساعة 2:00 مساءً',
        type: 'visit',
        entityId: 'VIS-001',
        entityType: 'visit',
        priority: 'medium',
        isRead: true,
        readAt: now.subtract(const Duration(hours: 1)),
        createdAt: now.subtract(const Duration(hours: 4)),
        data: {'visitId': 'VIS-001', 'customerId': 'CUST-002'},
        icon: 'event',
      ),
      models.Notification(
        id: '4',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام إلى الإصدار 2.1.0 مع ميزات جديدة',
        type: 'system',
        priority: 'low',
        isRead: true,
        readAt: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 1)),
        data: {'version': '2.1.0'},
        icon: 'system_update',
      ),
      models.Notification(
        id: '5',
        title: 'عرض خاص',
        message: 'خصم 20% على جميع المنتجات لفترة محدودة',
        type: 'general',
        priority: 'medium',
        isRead: false,
        createdAt: now.subtract(const Duration(days: 2)),
        data: {'discount': '20%'},
        icon: 'local_offer',
      ),
    ];
  }
}
