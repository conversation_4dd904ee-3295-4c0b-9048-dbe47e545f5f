import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/rtl_utils.dart';

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final String route;
  final int? badgeCount;

  const NavigationItem({
    required this.icon,
    this.activeIcon,
    required this.label,
    required this.route,
    this.badgeCount,
  });
}

/// Custom bottom navigation bar following Al Ameen design system
class CustomBottomNavigation extends StatelessWidget {
  final List<NavigationItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? elevation;
  final bool showLabels;
  final NavigationBarType type;

  const CustomBottomNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
    this.showLabels = true,
    this.type = NavigationBarType.material3,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case NavigationBarType.material3:
        return _buildMaterial3Navigation();
      case NavigationBarType.classic:
        return _buildClassicNavigation();
    }
  }

  Widget _buildMaterial3Navigation() {
    return NavigationBar(
      selectedIndex: currentIndex,
      onDestinationSelected: onTap,
      backgroundColor: backgroundColor ?? AppColors.surface,
      elevation: elevation ?? 3,
      height: 80.h,
      destinations: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isSelected = index == currentIndex;

        return NavigationDestination(
          icon: _buildIcon(item, isSelected, false),
          selectedIcon: _buildIcon(item, isSelected, true),
          label: item.label.tr,
        );
      }).toList(),
    );
  }

  Widget _buildClassicNavigation() {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      backgroundColor: backgroundColor ?? AppColors.surface,
      selectedItemColor: selectedItemColor ?? AppColors.primary,
      unselectedItemColor: unselectedItemColor ?? AppColors.onSurfaceVariant,
      elevation: elevation ?? 8,
      type: BottomNavigationBarType.fixed,
      showSelectedLabels: showLabels,
      showUnselectedLabels: showLabels,
      selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTextStyles.labelSmall,
      items: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isSelected = index == currentIndex;

        return BottomNavigationBarItem(
          icon: _buildIcon(item, isSelected, false),
          activeIcon: _buildIcon(item, isSelected, true),
          label: item.label.tr,
        );
      }).toList(),
    );
  }

  Widget _buildIcon(NavigationItem item, bool isSelected, bool isActiveIcon) {
    IconData iconData;
    
    if (isActiveIcon && item.activeIcon != null) {
      iconData = item.activeIcon!;
    } else {
      iconData = item.icon;
    }

    // Apply RTL transformation if needed
    iconData = RTLUtils.getDirectionalIcon(iconData);

    Widget iconWidget = Icon(
      iconData,
      size: 24.w,
      color: isSelected 
          ? (selectedItemColor ?? AppColors.primary)
          : (unselectedItemColor ?? AppColors.onSurfaceVariant),
    );

    // Add badge if present
    if (item.badgeCount != null && item.badgeCount! > 0) {
      iconWidget = Badge(
        label: Text(
          item.badgeCount! > 99 ? '99+' : item.badgeCount.toString(),
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.onPrimary,
            fontSize: 10.sp,
          ),
        ),
        backgroundColor: AppColors.error,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}

/// Navigation bar types
enum NavigationBarType {
  material3,
  classic,
}

/// Navigation controller for managing bottom navigation state
class NavigationController extends GetxController {
  final RxInt currentIndex = 0.obs;
  final List<NavigationItem> items;

  NavigationController({required this.items});

  void changePage(int index) {
    if (index >= 0 && index < items.length) {
      currentIndex.value = index;
      Get.offAllNamed(items[index].route);
    }
  }

  void updateBadge(int index, int? count) {
    if (index >= 0 && index < items.length) {
      // This would typically update the badge count in a more complex implementation
      // For now, we'll just trigger a rebuild
      update();
    }
  }

  NavigationItem get currentItem => items[currentIndex.value];
  
  bool get canGoBack => currentIndex.value > 0;
  
  void goBack() {
    if (canGoBack) {
      changePage(currentIndex.value - 1);
    }
  }
}

/// Predefined navigation items for Al Ameen Sales App
class AppNavigationItems {
  static const dashboard = NavigationItem(
    icon: Icons.dashboard_outlined,
    activeIcon: Icons.dashboard,
    label: 'dashboard',
    route: '/dashboard',
  );

  static const orders = NavigationItem(
    icon: Icons.shopping_cart_outlined,
    activeIcon: Icons.shopping_cart,
    label: 'orders',
    route: '/orders',
  );

  static const customers = NavigationItem(
    icon: Icons.people_outline,
    activeIcon: Icons.people,
    label: 'customers',
    route: '/customers',
  );

  static const visits = NavigationItem(
    icon: Icons.location_on_outlined,
    activeIcon: Icons.location_on,
    label: 'visits',
    route: '/visits',
  );

  static const invoices = NavigationItem(
    icon: Icons.receipt_outlined,
    activeIcon: Icons.receipt,
    label: 'invoices',
    route: '/invoices',
  );

  static const returns = NavigationItem(
    icon: Icons.keyboard_return_outlined,
    activeIcon: Icons.keyboard_return,
    label: 'returns',
    route: '/returns',
  );

  static const notifications = NavigationItem(
    icon: Icons.notifications_outlined,
    activeIcon: Icons.notifications,
    label: 'notifications',
    route: '/notifications',
  );

  static const profile = NavigationItem(
    icon: Icons.person_outline,
    activeIcon: Icons.person,
    label: 'profile',
    route: '/profile',
  );

  static const settings = NavigationItem(
    icon: Icons.settings_outlined,
    activeIcon: Icons.settings,
    label: 'settings',
    route: '/settings',
  );

  /// Get retail navigation items
  static List<NavigationItem> get retailItems => [
    dashboard,
    orders,
    customers,
    visits,
    profile,
  ];

  /// Get admin navigation items
  static List<NavigationItem> get adminItems => [
    dashboard,
    orders,
    customers,
    invoices,
    returns,
  ];
}
