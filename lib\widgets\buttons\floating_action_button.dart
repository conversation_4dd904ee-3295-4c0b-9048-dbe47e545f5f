import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';

/// Custom floating action button following Al Ameen design system
class CustomFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final IconData? icon;
  final String? label;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool isExtended;
  final bool isLoading;
  final FloatingActionButtonType type;
  final FloatingActionButtonSize size;

  const CustomFloatingActionButton({
    super.key,
    this.onPressed,
    this.child,
    this.icon,
    this.label,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.isExtended = false,
    this.isLoading = false,
    this.type = FloatingActionButtonType.primary,
    this.size = FloatingActionButtonSize.regular,
  });

  @override
  Widget build(BuildContext context) {
    if (isExtended && label != null) {
      return _buildExtendedFAB();
    } else {
      return _buildRegularFAB();
    }
  }

  Widget _buildExtendedFAB() {
    return FloatingActionButton.extended(
      onPressed: isLoading ? null : onPressed,
      backgroundColor: _getBackgroundColor(),
      foregroundColor: _getForegroundColor(),
      elevation: elevation ?? _getDefaultElevation(),
      tooltip: tooltip?.tr,
      icon: _buildIcon(),
      label: Text(
        label!.tr,
        style: AppTextStyles.labelLarge.copyWith(
          color: _getForegroundColor(),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildRegularFAB() {
    switch (size) {
      case FloatingActionButtonSize.small:
        return FloatingActionButton.small(
          onPressed: isLoading ? null : onPressed,
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          elevation: elevation ?? _getDefaultElevation(),
          tooltip: tooltip?.tr ?? label?.tr,
          child: _buildIcon(),
        );
      case FloatingActionButtonSize.regular:
        return FloatingActionButton(
          onPressed: isLoading ? null : onPressed,
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          elevation: elevation ?? _getDefaultElevation(),
          tooltip: tooltip?.tr ?? label?.tr,
          child: _buildIcon(),
        );
      case FloatingActionButtonSize.large:
        return FloatingActionButton.large(
          onPressed: isLoading ? null : onPressed,
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          elevation: elevation ?? _getDefaultElevation(),
          tooltip: tooltip?.tr ?? label?.tr,
          child: _buildIcon(),
        );
    }
  }

  Widget _buildIcon() {
    if (isLoading) {
      return SizedBox(
        width: _getLoadingSize(),
        height: _getLoadingSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getForegroundColor()),
        ),
      );
    }

    if (child != null) {
      return child!;
    }

    if (icon != null) {
      return Icon(
        icon,
        size: _getIconSize(),
      );
    }

    return Icon(
      Icons.add,
      size: _getIconSize(),
    );
  }

  Color _getBackgroundColor() {
    if (backgroundColor != null) return backgroundColor!;
    
    switch (type) {
      case FloatingActionButtonType.primary:
        return AppColors.primary;
      case FloatingActionButtonType.secondary:
        return AppColors.secondary;
      case FloatingActionButtonType.surface:
        return AppColors.surface;
      case FloatingActionButtonType.error:
        return AppColors.error;
    }
  }

  Color _getForegroundColor() {
    if (foregroundColor != null) return foregroundColor!;
    
    switch (type) {
      case FloatingActionButtonType.primary:
        return AppColors.onPrimary;
      case FloatingActionButtonType.secondary:
        return AppColors.onSecondary;
      case FloatingActionButtonType.surface:
        return AppColors.onSurface;
      case FloatingActionButtonType.error:
        return AppColors.onPrimary;
    }
  }

  double _getDefaultElevation() {
    switch (type) {
      case FloatingActionButtonType.primary:
      case FloatingActionButtonType.error:
        return 6;
      case FloatingActionButtonType.secondary:
        return 3;
      case FloatingActionButtonType.surface:
        return 1;
    }
  }

  double _getIconSize() {
    switch (size) {
      case FloatingActionButtonSize.small:
        return 20.w;
      case FloatingActionButtonSize.regular:
        return 24.w;
      case FloatingActionButtonSize.large:
        return 28.w;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case FloatingActionButtonSize.small:
        return 16.w;
      case FloatingActionButtonSize.regular:
        return 20.w;
      case FloatingActionButtonSize.large:
        return 24.w;
    }
  }
}

/// Floating action button types
enum FloatingActionButtonType {
  primary,
  secondary,
  surface,
  error,
}

/// Floating action button sizes
enum FloatingActionButtonSize {
  small,
  regular,
  large,
}

/// Multi-action floating action button with expandable menu
class MultiActionFAB extends StatefulWidget {
  final List<FABAction> actions;
  final IconData? mainIcon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isOpen;
  final ValueChanged<bool>? onToggle;

  const MultiActionFAB({
    super.key,
    required this.actions,
    this.mainIcon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.isOpen = false,
    this.onToggle,
  });

  @override
  State<MultiActionFAB> createState() => _MultiActionFABState();
}

class _MultiActionFABState extends State<MultiActionFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastOutSlowIn,
    );
    _isOpen = widget.isOpen;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isOpen = !_isOpen;
      if (_isOpen) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onToggle?.call(_isOpen);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ..._buildActionButtons(),
        SizedBox(height: 16.h),
        _buildMainFAB(),
      ],
    );
  }

  List<Widget> _buildActionButtons() {
    return widget.actions.asMap().entries.map((entry) {
      final index = entry.key;
      final action = entry.value;
      
      return AnimatedBuilder(
        animation: _expandAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _expandAnimation.value,
            child: Opacity(
              opacity: _expandAnimation.value,
              child: Container(
                margin: EdgeInsets.only(bottom: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (action.label != null) ...[
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(16.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.shadowLight,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          action.label!.tr,
                          style: AppTextStyles.labelSmall.copyWith(
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                      SizedBox(width: 16.w),
                    ],
                    FloatingActionButton.small(
                      onPressed: () {
                        action.onPressed?.call();
                        _toggle();
                      },
                      backgroundColor: action.backgroundColor ?? AppColors.surface,
                      foregroundColor: action.foregroundColor ?? AppColors.onSurface,
                      heroTag: 'fab_action_$index',
                      child: Icon(action.icon),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  Widget _buildMainFAB() {
    return FloatingActionButton(
      onPressed: _toggle,
      backgroundColor: widget.backgroundColor ?? AppColors.primary,
      foregroundColor: widget.foregroundColor ?? AppColors.onPrimary,
      tooltip: widget.tooltip?.tr,
      child: AnimatedRotation(
        turns: _isOpen ? 0.125 : 0.0,
        duration: const Duration(milliseconds: 250),
        child: Icon(
          _isOpen ? Icons.close : (widget.mainIcon ?? Icons.add),
        ),
      ),
    );
  }
}

/// Action item for multi-action FAB
class FABAction {
  final IconData icon;
  final String? label;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const FABAction({
    required this.icon,
    this.label,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
  });
}

/// Predefined FAB actions for Al Ameen Sales App
class AppFABActions {
  static FABAction get addOrder => FABAction(
    icon: Icons.add_shopping_cart,
    label: 'add_order',
    onPressed: () => Get.toNamed('/orders/create'),
  );

  static FABAction get addCustomer => FABAction(
    icon: Icons.person_add,
    label: 'add_customer',
    onPressed: () => Get.toNamed('/customers/create'),
  );

  static FABAction get scheduleVisit => FABAction(
    icon: Icons.schedule,
    label: 'schedule_visit',
    onPressed: () => Get.toNamed('/visits/create'),
  );

  static FABAction get createInvoice => FABAction(
    icon: Icons.receipt_long,
    label: 'create_invoice',
    onPressed: () => Get.toNamed('/invoices/create'),
  );

  static List<FABAction> get salesActions => [
    addOrder,
    addCustomer,
    scheduleVisit,
  ];

  static List<FABAction> get adminActions => [
    addOrder,
    addCustomer,
    scheduleVisit,
    createInvoice,
  ];
}
