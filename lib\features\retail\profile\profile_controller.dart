import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/user.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/services/auth_service.dart';

/// Controller for profile management
class ProfileController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Observable variables
  final isLoading = false.obs;
  final isEditing = false.obs;
  final user = Rxn<User>();

  // Form controllers
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final addressController = TextEditingController();

  // Form key
  final formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    addressController.dispose();
    super.onClose();
  }

  /// Load user profile
  Future<void> loadUserProfile() async {
    try {
      isLoading.value = true;
      
      // Get current user from auth service
      user.value = _authService.currentUser.value;
      
      if (user.value != null) {
        _populateFormFields();
      }
      
      // TODO: Replace with actual API call if needed
      // user.value = await _userService.getCurrentUser();
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_profile'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Populate form fields with user data
  void _populateFormFields() {
    if (user.value != null) {
      nameController.text = user.value!.name;
      emailController.text = user.value!.email;
      phoneController.text = user.value!.phone ?? '';
      addressController.text = user.value!.address ?? '';
    }
  }

  /// Toggle edit mode
  void toggleEditMode() {
    isEditing.value = !isEditing.value;
    if (!isEditing.value) {
      // Reset form fields if canceling edit
      _populateFormFields();
    }
  }

  /// Update user profile
  Future<void> updateProfile() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isLoading.value = true;

      final updatedUser = user.value!.copyWith(
        name: nameController.text.trim(),
        email: emailController.text.trim(),
        phone: phoneController.text.trim().isEmpty ? null : phoneController.text.trim(),
        address: addressController.text.trim().isEmpty ? null : addressController.text.trim(),
      );

      // TODO: Call API to update profile
      // await _userService.updateProfile(updatedUser);

      // Update local user data
      user.value = updatedUser;
      _authService.currentUser.value = updatedUser;

      isEditing.value = false;
      Helpers.showSuccessSnackbar('profile_updated_successfully'.tr);

    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_profile'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Change password
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      isLoading.value = true;

      // TODO: Call API to change password
      // await _authService.changePassword(currentPassword, newPassword);

      Helpers.showSuccessSnackbar('password_changed_successfully'.tr);
      Get.back(); // Close change password dialog

    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_change_password'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_logout'.tr),
          content: Text('confirm_logout_message'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('cancel'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('logout'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        isLoading.value = true;
        await _authService.logout();
        // Navigation will be handled by AuthMiddleware
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_logout'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Navigate to settings
  void navigateToSettings() {
    Get.toNamed('/retail/settings');
  }

  /// Navigate to change password
  void navigateToChangePassword() {
    _showChangePasswordDialog();
  }

  /// Show change password dialog
  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('change_password'.tr),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: currentPasswordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'current_password'.tr,
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'current_password_required'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: newPasswordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'new_password'.tr,
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'new_password_required'.tr;
                    }
                    if (value.length < 6) {
                      return 'password_min_length'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: confirmPasswordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'confirm_password'.tr,
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'confirm_password_required'.tr;
                    }
                    if (value != newPasswordController.text) {
                      return 'passwords_do_not_match'.tr;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('cancel'.tr),
            ),
            Obx(() => ElevatedButton(
              onPressed: isLoading.value
                  ? null
                  : () {
                      if (formKey.currentState!.validate()) {
                        changePassword(
                          currentPasswordController.text,
                          newPasswordController.text,
                        );
                      }
                    },
              child: isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text('change_password'.tr),
            )),
          ],
        ),
      ),
    );
  }

  /// Get user statistics (mock data)
  Map<String, int> get userStatistics {
    return {
      'total_orders': 45,
      'total_customers': 23,
      'total_visits': 67,
      'total_invoices': 38,
    };
  }

  /// Validate name field
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'name_required'.tr;
    }
    if (value.trim().length < 2) {
      return 'name_min_length'.tr;
    }
    return null;
  }

  /// Validate email field
  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'email_required'.tr;
    }
    if (!GetUtils.isEmail(value.trim())) {
      return 'invalid_email'.tr;
    }
    return null;
  }

  /// Validate phone field (optional)
  String? validatePhone(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      if (!GetUtils.isPhoneNumber(value.trim())) {
        return 'invalid_phone'.tr;
      }
    }
    return null;
  }
}
