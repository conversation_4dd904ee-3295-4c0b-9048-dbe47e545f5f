import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/customer.dart';

/// Customer card widget for displaying customer information
class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Ink<PERSON>ell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            customer.name,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            customer.customerCode,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Status and type chips
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildStatusChip(),
                        SizedBox(height: 4.h),
                        _buildTypeChip(),
                      ],
                    ),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18.sp),
                              SizedBox(width: 8.w),
                              Text('edit'.tr),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18.sp, color: AppColors.error),
                              SizedBox(width: 8.w),
                              Text('delete'.tr, style: TextStyle(color: AppColors.error)),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(
                        Icons.more_vert,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Customer details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.person,
                        label: 'contact_person'.tr,
                        value: customer.contactPerson ?? customer.name,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.phone,
                        label: 'phone_number'.tr,
                        value: customer.phone ?? 'غير محدد',
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.account_balance_wallet,
                        label: 'current_balance'.tr,
                        value: FormatUtils.formatCurrency(customer.currentBalance),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.credit_card,
                        label: 'credit_limit'.tr,
                        value: FormatUtils.formatCurrency(customer.creditLimit),
                      ),
                    ),
                  ],
                ),
                
                if (customer.address != null) ...[
                  SizedBox(height: 12.h),
                  _buildDetailItem(
                    icon: Icons.location_on,
                    label: 'address'.tr,
                    value: '${customer.address!.city}, ${customer.address!.state}',
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    String statusText;

    switch (customer.status) {
      case 'active':
        statusColor = AppColors.success;
        statusText = 'active'.tr;
        break;
      case 'inactive':
        statusColor = AppColors.error;
        statusText = 'inactive'.tr;
        break;
      default:
        statusColor = AppColors.grey500;
        statusText = customer.status;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: AppTextStyles.labelSmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTypeChip() {
    Color typeColor;
    String typeText;

    switch (customer.type) {
      case 'retail':
        typeColor = AppColors.primary;
        typeText = 'retail'.tr;
        break;
      case 'wholesale':
        typeColor = AppColors.secondary;
        typeText = 'wholesale'.tr;
        break;
      case 'distributor':
        typeColor = AppColors.accent;
        typeText = 'distributor'.tr;
        break;
      default:
        typeColor = AppColors.grey500;
        typeText = customer.type;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: typeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        typeText,
        style: AppTextStyles.labelSmall.copyWith(
          color: typeColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: AppColors.onSurfaceVariant,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
