import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/rtl_utils.dart';

/// Custom app bar following Al Ameen design system
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;
  final double? leadingWidth;
  final double? titleSpacing;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final AppBarType type;

  const CustomAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.bottom,
    this.leadingWidth,
    this.titleSpacing,
    this.showBackButton = true,
    this.onBackPressed,
    this.type = AppBarType.standard,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case AppBarType.standard:
        return _buildStandardAppBar(context);
      case AppBarType.large:
        return _buildLargeAppBar(context);
      case AppBarType.medium:
        return _buildMediumAppBar(context);
      case AppBarType.small:
        return _buildSmallAppBar(context);
    }
  }

  Widget _buildStandardAppBar(BuildContext context) {
    return AppBar(
      title: _buildTitle(),
      actions: _buildActions(),
      leading: _buildLeading(context),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.onPrimary,
      elevation: elevation ?? 2,
      centerTitle: centerTitle,
      bottom: bottom,
      leadingWidth: leadingWidth,
      titleSpacing: titleSpacing,
      surfaceTintColor: Colors.transparent,
    );
  }

  Widget _buildLargeAppBar(BuildContext context) {
    return SliverAppBar.large(
      title: _buildTitle(),
      actions: _buildActions(),
      leading: _buildLeading(context),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.onPrimary,
      elevation: elevation ?? 0,
      centerTitle: centerTitle,
      bottom: bottom,
      leadingWidth: leadingWidth,
      titleSpacing: titleSpacing,
      surfaceTintColor: Colors.transparent,
    );
  }

  Widget _buildMediumAppBar(BuildContext context) {
    return SliverAppBar.medium(
      title: _buildTitle(),
      actions: _buildActions(),
      leading: _buildLeading(context),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.onPrimary,
      elevation: elevation ?? 0,
      centerTitle: centerTitle,
      bottom: bottom,
      leadingWidth: leadingWidth,
      titleSpacing: titleSpacing,
      surfaceTintColor: Colors.transparent,
    );
  }

  Widget _buildSmallAppBar(BuildContext context) {
    return AppBar(
      title: _buildTitle(),
      actions: _buildActions(),
      leading: _buildLeading(context),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? AppColors.surface,
      foregroundColor: foregroundColor ?? AppColors.onSurface,
      elevation: elevation ?? 1,
      centerTitle: centerTitle,
      bottom: bottom,
      leadingWidth: leadingWidth,
      titleSpacing: titleSpacing,
      surfaceTintColor: Colors.transparent,
      toolbarHeight: 48.h,
    );
  }

  Widget? _buildTitle() {
    if (titleWidget != null) {
      return titleWidget;
    }
    
    if (title != null) {
      return Text(
        title!.tr,
        style: AppTextStyles.titleLarge.copyWith(
          color: foregroundColor ?? AppColors.onPrimary,
          fontWeight: FontWeight.w600,
        ),
      );
    }
    
    return null;
  }

  List<Widget>? _buildActions() {
    if (actions == null) return null;
    
    return actions!.map((action) {
      if (action is IconButton) {
        return IconButton(
          onPressed: action.onPressed,
          icon: action.icon,
          color: foregroundColor ?? AppColors.onPrimary,
          iconSize: action.iconSize ?? 24.w,
        );
      }
      return action;
    }).toList();
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) {
      return leading;
    }

    if (showBackButton && Navigator.canPop(context)) {
      return IconButton(
        onPressed: onBackPressed ?? () => Get.back(),
        icon: Icon(
          RTLUtils.getDirectionalIcon(Icons.arrow_back),
          color: foregroundColor ?? AppColors.onPrimary,
        ),
      );
    }

    return null;
  }

  @override
  Size get preferredSize {
    double height = kToolbarHeight;
    
    switch (type) {
      case AppBarType.standard:
        height = kToolbarHeight;
        break;
      case AppBarType.large:
        height = 152.h;
        break;
      case AppBarType.medium:
        height = 112.h;
        break;
      case AppBarType.small:
        height = 48.h;
        break;
    }
    
    if (bottom != null) {
      height += bottom!.preferredSize.height;
    }
    
    return Size.fromHeight(height);
  }
}

/// App bar types
enum AppBarType {
  standard,
  large,
  medium,
  small,
}

/// Search app bar for search functionality
class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final VoidCallback? onBack;
  final List<Widget>? actions;
  final bool autofocus;
  final TextEditingController? controller;

  const SearchAppBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onClear,
    this.onBack,
    this.actions,
    this.autofocus = true,
    this.controller,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _SearchAppBarState extends State<SearchAppBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = FocusNode();
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.surface,
      elevation: 1,
      leading: IconButton(
        onPressed: widget.onBack ?? () => Get.back(),
        icon: Icon(
          RTLUtils.getDirectionalIcon(Icons.arrow_back),
          color: AppColors.onSurface,
        ),
      ),
      title: TextField(
        controller: _controller,
        focusNode: _focusNode,
        onChanged: widget.onChanged,
        style: AppTextStyles.bodyLarge.copyWith(
          color: AppColors.onSurface,
        ),
        decoration: InputDecoration(
          hintText: widget.hintText?.tr ?? 'search'.tr,
          hintStyle: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          border: InputBorder.none,
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _controller.clear();
                    widget.onClear?.call();
                    widget.onChanged?.call('');
                  },
                  icon: const Icon(
                    Icons.clear,
                    color: AppColors.onSurfaceVariant,
                  ),
                )
              : null,
        ),
      ),
      actions: widget.actions,
    );
  }
}
