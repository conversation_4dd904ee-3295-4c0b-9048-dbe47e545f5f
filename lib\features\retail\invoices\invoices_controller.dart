import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/invoice.dart';
import '../../../core/utils/helpers.dart';

/// Controller for invoices management
class InvoicesController extends GetxController {
  // Observable variables
  final isLoading = false.obs;
  final invoices = <Invoice>[].obs;
  final filteredInvoices = <Invoice>[].obs;
  final searchQuery = ''.obs;
  final selectedStatus = 'all'.obs;
  final selectedDateRange = 'all'.obs;

  // Filter options
  final statusOptions = [
    'all',
    'draft',
    'sent',
    'paid',
    'overdue',
    'cancelled'
  ].obs;

  final dateRangeOptions = [
    'all',
    'today',
    'this_week',
    'this_month',
    'last_month',
    'last_quarter'
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadInvoices();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedDateRange, (_) => _applyFilters());
  }

  /// Load invoices from service
  Future<void> loadInvoices() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      invoices.value = _generateMockInvoices();
      
      // TODO: Replace with actual API call
      // invoices.value = await _invoiceService.getInvoices();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_invoices'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = invoices.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((invoice) {
        return invoice.invoiceNumber.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               invoice.customerName.toLowerCase().contains(searchQuery.value.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      filtered = filtered.where((invoice) => invoice.status == selectedStatus.value).toList();
    }

    // Apply date range filter
    if (selectedDateRange.value != 'all') {
      final now = DateTime.now();
      DateTime? startDate;

      switch (selectedDateRange.value) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case 'this_week':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          break;
        case 'this_month':
          startDate = DateTime(now.year, now.month, 1);
          break;
        case 'last_month':
          final lastMonth = DateTime(now.year, now.month - 1, 1);
          startDate = lastMonth;
          break;
        case 'last_quarter':
          final quarterStart = DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1, 1);
          startDate = quarterStart.subtract(const Duration(days: 90));
          break;
      }

      if (startDate != null) {
        filtered = filtered.where((invoice) => invoice.invoiceDate.isAfter(startDate!)).toList();
      }
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.invoiceDate.compareTo(a.invoiceDate));

    filteredInvoices.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update date range filter
  void updateDateRangeFilter(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
    selectedDateRange.value = 'all';
  }

  /// Navigate to invoice details
  void navigateToInvoiceDetails(String invoiceId) {
    Get.toNamed('/retail/invoices/$invoiceId');
  }

  /// Navigate to create invoice
  void navigateToCreateInvoice() {
    Get.toNamed('/retail/invoices/create');
  }

  /// Navigate to edit invoice
  void navigateToEditInvoice(String invoiceId) {
    Get.toNamed('/retail/invoices/$invoiceId/edit');
  }

  /// Mark invoice as sent
  Future<void> markAsSent(String invoiceId) async {
    try {
      // TODO: Call API to update invoice status
      // await _invoiceService.updateInvoiceStatus(invoiceId, 'sent');
      
      // Update local list
      final invoiceIndex = invoices.indexWhere((invoice) => invoice.id == invoiceId);
      if (invoiceIndex != -1) {
        invoices[invoiceIndex] = invoices[invoiceIndex].copyWith(
          status: 'sent',
          sentDate: DateTime.now(),
        );
        _applyFilters();
      }
      
      Helpers.showSuccessSnackbar('invoice_sent_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_send_invoice'.tr);
    }
  }

  /// Mark invoice as paid
  Future<void> markAsPaid(String invoiceId, double paidAmount) async {
    try {
      // TODO: Call API to update invoice payment
      // await _invoiceService.recordPayment(invoiceId, paidAmount);
      
      // Update local list
      final invoiceIndex = invoices.indexWhere((invoice) => invoice.id == invoiceId);
      if (invoiceIndex != -1) {
        invoices[invoiceIndex] = invoices[invoiceIndex].copyWith(
          status: 'paid',
          paidAmount: paidAmount,
          paidDate: DateTime.now(),
        );
        _applyFilters();
      }
      
      Helpers.showSuccessSnackbar('payment_recorded_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_record_payment'.tr);
    }
  }

  /// Cancel invoice
  Future<void> cancelInvoice(String invoiceId, String reason) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_cancel'.tr),
          content: Text('confirm_cancel_invoice'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('no'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('yes'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to cancel invoice
        // await _invoiceService.cancelInvoice(invoiceId, reason);
        
        // Update local list
        final invoiceIndex = invoices.indexWhere((invoice) => invoice.id == invoiceId);
        if (invoiceIndex != -1) {
          invoices[invoiceIndex] = invoices[invoiceIndex].copyWith(
            status: 'cancelled',
            notes: reason,
          );
          _applyFilters();
        }
        
        Helpers.showSuccessSnackbar('invoice_cancelled_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_cancel_invoice'.tr);
    }
  }

  /// Print invoice
  void printInvoice(String invoiceId) {
    // TODO: Implement print functionality
    Helpers.showInfoSnackbar('print_feature_coming_soon'.tr);
  }

  /// Share invoice
  void shareInvoice(String invoiceId) {
    // TODO: Implement share functionality
    Helpers.showInfoSnackbar('share_feature_coming_soon'.tr);
  }

  /// Refresh invoices
  Future<void> refreshInvoices() async {
    await loadInvoices();
  }

  /// Get total amount for filtered invoices
  double get totalAmount {
    return filteredInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
  }

  /// Get paid amount for filtered invoices
  double get paidAmount {
    return filteredInvoices.fold(0.0, (sum, invoice) => sum + invoice.paidAmount);
  }

  /// Get outstanding amount for filtered invoices
  double get outstandingAmount {
    return totalAmount - paidAmount;
  }

  /// Generate mock invoices for demonstration
  List<Invoice> _generateMockInvoices() {
    final now = DateTime.now();
    return [
      Invoice(
        id: '1',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        customerName: 'محمد أحمد التجاري',
        invoiceDate: now.subtract(const Duration(days: 2)),
        dueDate: now.add(const Duration(days: 28)),
        status: 'sent',
        subtotal: 1000.0,
        taxAmount: 150.0,
        discountAmount: 50.0,
        totalAmount: 1100.0,
        paidAmount: 0.0,
        balanceAmount: 1100.0,
        paymentTerms: 'Net 30',
        items: [],
        notes: 'فاتورة للطلب رقم ORD-001',
        createdBy: 'USER-001',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 1)),
        sentAt: now.subtract(const Duration(days: 1)),
      ),
      Invoice(
        id: '2',
        invoiceNumber: 'INV-002',
        customerId: 'CUST-002',
        customerName: 'فاطمة علي للتجارة',
        invoiceDate: now.subtract(const Duration(days: 5)),
        dueDate: now.subtract(const Duration(days: 5)),
        status: 'overdue',
        subtotal: 2000.0,
        taxAmount: 300.0,
        discountAmount: 0.0,
        totalAmount: 2300.0,
        paidAmount: 0.0,
        balanceAmount: 2300.0,
        paymentTerms: 'Net 15',
        items: [],
        notes: '',
        createdBy: 'USER-001',
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 4)),
        sentAt: now.subtract(const Duration(days: 4)),
      ),
      Invoice(
        id: '3',
        invoiceNumber: 'INV-003',
        customerId: 'CUST-003',
        customerName: 'عبدالله محمد المؤسسة',
        invoiceDate: now.subtract(const Duration(days: 10)),
        dueDate: now.subtract(const Duration(days: 10)),
        status: 'paid',
        subtotal: 1500.0,
        taxAmount: 225.0,
        discountAmount: 75.0,
        totalAmount: 1650.0,
        paidAmount: 1650.0,
        balanceAmount: 0.0,
        paymentTerms: 'Cash',
        items: [],
        notes: 'تم الدفع نقداً',
        createdBy: 'USER-001',
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 8)),
        sentAt: now.subtract(const Duration(days: 9)),
        paidAt: now.subtract(const Duration(days: 8)),
      ),
    ];
  }
}
