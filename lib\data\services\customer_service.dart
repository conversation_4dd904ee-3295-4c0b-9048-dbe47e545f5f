import 'package:get/get.dart';
import '../models/customer.dart';
import '../../core/config/app_config.dart';
import '../../core/constants/api_endpoints.dart';
import '../../core/exceptions/app_exception.dart';

/// Customer service for Al Ameen Sales App
class CustomerService extends GetConnect {
  @override
  void onInit() {
    super.onInit();
    
    // Configure base URL
    httpClient.baseUrl = AppConfig.baseUrl;
    
    // Configure timeouts
    httpClient.timeout = Duration(milliseconds: AppConfig.connectTimeout);
    
    // Add request interceptor for authentication
    httpClient.addRequestModifier<dynamic>((request) async {
      // Token will be added by auth interceptor
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      return request;
    });
  }

  /// Get customers with pagination and filters
  Future<List<Customer>> getCustomers({
    int page = 1,
    int limit = 20,
    CustomerFilters? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        ...?filters?.toQueryParams(),
      };

      final response = await get(ApiEndpoints.customers, query: queryParams);

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Customer.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customers: ${e.toString()}');
    }
  }

  /// Get customer by ID
  Future<Customer> getCustomer(String customerId) async {
    try {
      final response = await get(ApiEndpoints.getCustomerDetails(customerId));

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Customer.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customer: ${e.toString()}');
    }
  }

  /// Create new customer
  Future<Customer> createCustomer(Map<String, dynamic> customerData) async {
    try {
      final response = await post(ApiEndpoints.createCustomer, customerData);

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Customer.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to create customer: ${e.toString()}');
    }
  }

  /// Update customer
  Future<Customer> updateCustomer(String customerId, Map<String, dynamic> customerData) async {
    try {
      final response = await put(
        ApiEndpoints.getCustomerDetails(customerId),
        customerData,
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Customer.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to update customer: ${e.toString()}');
    }
  }

  /// Delete customer (soft delete)
  Future<void> deleteCustomer(String customerId) async {
    try {
      final response = await delete(ApiEndpoints.getCustomerDetails(customerId));

      if (response.hasError) {
        throw _handleError(response);
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to delete customer: ${e.toString()}');
    }
  }

  /// Search customers
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      final response = await get(
        ApiEndpoints.customerSearch,
        query: {'q': query},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Customer.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to search customers: ${e.toString()}');
    }
  }

  /// Get customer order history
  Future<List<dynamic>> getCustomerOrderHistory(String customerId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await get(
        ApiEndpoints.getCustomerOrderHistory(customerId),
        query: {'page': page, 'limit': limit},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      return response.body['data'] as List;
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customer order history: ${e.toString()}');
    }
  }

  /// Get customer statistics
  Future<CustomerStats> getCustomerStats(String customerId) async {
    try {
      final response = await get('${ApiEndpoints.getCustomerDetails(customerId)}/stats');

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return CustomerStats.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customer stats: ${e.toString()}');
    }
  }

  /// Get customers by type
  Future<List<Customer>> getCustomersByType(String type, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await get(
        ApiEndpoints.customers,
        query: {
          'type': type,
          'page': page,
          'limit': limit,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Customer.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customers by type: ${e.toString()}');
    }
  }

  /// Get customers by status
  Future<List<Customer>> getCustomersByStatus(String status, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await get(
        ApiEndpoints.customers,
        query: {
          'status': status,
          'page': page,
          'limit': limit,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Customer.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customers by status: ${e.toString()}');
    }
  }

  /// Update customer status
  Future<Customer> updateCustomerStatus(String customerId, String status) async {
    try {
      final response = await patch(
        '${ApiEndpoints.getCustomerDetails(customerId)}/status',
        {'status': status},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Customer.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to update customer status: ${e.toString()}');
    }
  }

  /// Get nearby customers (if location is available)
  Future<List<Customer>> getNearbyCustomers({
    required double latitude,
    required double longitude,
    double radius = 10.0, // km
    int limit = 20,
  }) async {
    try {
      final response = await get(
        '${ApiEndpoints.customers}/nearby',
        query: {
          'latitude': latitude,
          'longitude': longitude,
          'radius': radius,
          'limit': limit,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Customer.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch nearby customers: ${e.toString()}');
    }
  }

  /// Export customers data
  Future<String> exportCustomers({
    String format = 'csv', // csv, excel, pdf
    CustomerFilters? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'format': format,
        ...?filters?.toQueryParams(),
      };

      final response = await get(
        '${ApiEndpoints.customers}/export',
        query: queryParams,
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      return response.body['data']['download_url'];
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to export customers: ${e.toString()}');
    }
  }

  /// Handle API errors
  AppException _handleError(Response response) {
    final statusCode = response.statusCode ?? 0;
    final body = response.body;
    
    String message = 'An error occurred';
    String? code;
    
    if (body is Map<String, dynamic>) {
      message = body['message'] ?? message;
      code = body['code'];
    }
    
    return ExceptionFactory.createFromHttpStatus(
      statusCode: statusCode,
      message: message,
      code: code,
      details: body,
    );
  }
}
