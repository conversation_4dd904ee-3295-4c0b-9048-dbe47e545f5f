import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';

/// Action button for quick actions and floating actions
class ActionButton extends StatelessWidget {
  final String? label;
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? iconColor;
  final double? size;
  final bool isExtended;
  final bool isLoading;
  final String? tooltip;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final ActionButtonType type;

  const ActionButton({
    super.key,
    this.label,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.iconColor,
    this.size,
    this.isExtended = false,
    this.isLoading = false,
    this.tooltip,
    this.padding,
    this.elevation,
    this.type = ActionButtonType.primary,
  });

  @override
  Widget build(BuildContext context) {
    if (isExtended && label != null) {
      return _buildExtendedButton();
    } else {
      return _buildCircularButton();
    }
  }

  Widget _buildExtendedButton() {
    return FloatingActionButton.extended(
      onPressed: isLoading ? null : onPressed,
      backgroundColor: _getBackgroundColor(),
      foregroundColor: _getForegroundColor(),
      elevation: elevation ?? _getDefaultElevation(),
      tooltip: tooltip,
      icon: _buildIcon(),
      label: Text(
        label!.tr,
        style: AppTextStyles.labelMedium.copyWith(
          color: _getForegroundColor(),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCircularButton() {
    return FloatingActionButton(
      onPressed: isLoading ? null : onPressed,
      backgroundColor: _getBackgroundColor(),
      foregroundColor: _getForegroundColor(),
      elevation: elevation ?? _getDefaultElevation(),
      tooltip: tooltip ?? label?.tr,
      child: _buildIcon(),
    );
  }

  Widget _buildIcon() {
    if (isLoading) {
      return SizedBox(
        width: 20.w,
        height: 20.w,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getForegroundColor()),
        ),
      );
    }

    return Icon(
      icon,
      size: size ?? 24.w,
      color: iconColor ?? _getForegroundColor(),
    );
  }

  Color _getBackgroundColor() {
    if (backgroundColor != null) return backgroundColor!;
    
    switch (type) {
      case ActionButtonType.primary:
        return AppColors.primary;
      case ActionButtonType.secondary:
        return AppColors.secondary;
      case ActionButtonType.surface:
        return AppColors.surface;
      case ActionButtonType.error:
        return AppColors.error;
    }
  }

  Color _getForegroundColor() {
    if (foregroundColor != null) return foregroundColor!;
    
    switch (type) {
      case ActionButtonType.primary:
        return AppColors.onPrimary;
      case ActionButtonType.secondary:
        return AppColors.onSecondary;
      case ActionButtonType.surface:
        return AppColors.onSurface;
      case ActionButtonType.error:
        return AppColors.onPrimary;
    }
  }

  double _getDefaultElevation() {
    switch (type) {
      case ActionButtonType.primary:
      case ActionButtonType.error:
        return 6;
      case ActionButtonType.secondary:
        return 3;
      case ActionButtonType.surface:
        return 1;
    }
  }
}

/// Action button types
enum ActionButtonType {
  primary,
  secondary,
  surface,
  error,
}

/// Quick action button for dashboard and other screens
class QuickActionButton extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? textColor;
  final bool isEnabled;
  final Widget? badge;

  const QuickActionButton({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.isEnabled = true,
    this.badge,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: backgroundColor ?? AppColors.surface,
      borderRadius: BorderRadius.circular(12.r),
      elevation: 2,
      shadowColor: AppColors.shadowLight,
      child: InkWell(
        onTap: isEnabled ? onPressed : null,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      icon,
                      size: 24.w,
                      color: isEnabled 
                          ? (iconColor ?? AppColors.primary)
                          : AppColors.onSurfaceVariant,
                    ),
                  ),
                  if (badge != null)
                    Positioned(
                      top: -4,
                      right: -4,
                      child: badge!,
                    ),
                ],
              ),
              SizedBox(height: 12.h),
              Text(
                title.tr,
                style: AppTextStyles.labelMedium.copyWith(
                  color: isEnabled 
                      ? (textColor ?? AppColors.onSurface)
                      : AppColors.onSurfaceVariant,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (subtitle != null) ...[
                SizedBox(height: 4.h),
                Text(
                  subtitle!.tr,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isEnabled 
                        ? AppColors.onSurfaceVariant
                        : AppColors.onSurfaceVariant.withValues(alpha: 0.6),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Icon action button for toolbars and app bars
class IconActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final Color? color;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final Widget? badge;
  final bool isEnabled;

  const IconActionButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.color,
    this.size,
    this.padding,
    this.badge,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget iconButton = IconButton(
      onPressed: isEnabled ? onPressed : null,
      icon: Icon(
        icon,
        size: size ?? 24.w,
        color: isEnabled 
            ? (color ?? AppColors.onSurface)
            : AppColors.onSurfaceVariant,
      ),
      tooltip: tooltip?.tr,
      padding: padding ?? EdgeInsets.all(8.w),
    );

    if (badge != null) {
      iconButton = Badge(
        label: badge,
        child: iconButton,
      );
    }

    return iconButton;
  }
}

/// Predefined action buttons for common actions
class AppActionButtons {
  static ActionButton get addOrder => ActionButton(
    icon: Icons.add_shopping_cart,
    label: 'add_order',
    tooltip: 'add_new_order',
    type: ActionButtonType.primary,
  );

  static ActionButton get addCustomer => ActionButton(
    icon: Icons.person_add,
    label: 'add_customer',
    tooltip: 'add_new_customer',
    type: ActionButtonType.secondary,
  );

  static ActionButton get scheduleVisit => ActionButton(
    icon: Icons.schedule,
    label: 'schedule_visit',
    tooltip: 'schedule_new_visit',
    type: ActionButtonType.surface,
  );

  static IconActionButton get search => IconActionButton(
    icon: Icons.search,
    tooltip: 'search',
  );

  static IconActionButton get filter => IconActionButton(
    icon: Icons.filter_list,
    tooltip: 'filter',
  );

  static IconActionButton get sort => IconActionButton(
    icon: Icons.sort,
    tooltip: 'sort',
  );

  static IconActionButton get refresh => IconActionButton(
    icon: Icons.refresh,
    tooltip: 'refresh',
  );

  static IconActionButton get more => IconActionButton(
    icon: Icons.more_vert,
    tooltip: 'more_options',
  );
}
