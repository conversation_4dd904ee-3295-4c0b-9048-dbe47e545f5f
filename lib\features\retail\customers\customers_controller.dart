import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/customer_service.dart';
import '../../../data/models/customer.dart';
import '../../../core/utils/helpers.dart';

/// Controller for customers management
class CustomersController extends GetxController {
  final CustomerService _customerService = Get.find<CustomerService>();

  // Observable variables
  final isLoading = false.obs;
  final customers = <Customer>[].obs;
  final filteredCustomers = <Customer>[].obs;
  final searchQuery = ''.obs;
  final selectedStatus = 'all'.obs;
  final selectedType = 'all'.obs;

  // Filter options
  final statusOptions = ['all', 'active', 'inactive'].obs;
  final typeOptions = ['all', 'retail', 'wholesale', 'distributor'].obs;

  @override
  void onInit() {
    super.onInit();
    loadCustomers();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedType, (_) => _applyFilters());
  }

  /// Load customers from service
  Future<void> loadCustomers() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      customers.value = _generateMockCustomers();
      
      // TODO: Replace with actual API call
      // customers.value = await _customerService.getCustomers();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_customers'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = customers.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((customer) {
        return customer.name.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               customer.customerCode.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               (customer.phone?.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      filtered = filtered.where((customer) => customer.status == selectedStatus.value).toList();
    }

    // Apply type filter
    if (selectedType.value != 'all') {
      filtered = filtered.where((customer) => customer.type == selectedType.value).toList();
    }

    // Sort by name
    filtered.sort((a, b) => a.name.compareTo(b.name));

    filteredCustomers.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update type filter
  void updateTypeFilter(String type) {
    selectedType.value = type;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
    selectedType.value = 'all';
  }

  /// Navigate to customer details
  void navigateToCustomerDetails(String customerId) {
    Get.toNamed('/retail/customers/$customerId');
  }

  /// Navigate to create customer
  void navigateToCreateCustomer() {
    Get.toNamed('/retail/customers/create');
  }

  /// Navigate to edit customer
  void navigateToEditCustomer(String customerId) {
    Get.toNamed('/retail/customers/$customerId/edit');
  }

  /// Delete customer
  Future<void> deleteCustomer(String customerId) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_delete'.tr),
          content: Text('confirm_delete_customer'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('cancel'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('delete'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to delete customer
        // await _customerService.deleteCustomer(customerId);
        
        // Remove from local list
        customers.removeWhere((customer) => customer.id == customerId);
        _applyFilters();
        
        Helpers.showSuccessSnackbar('customer_deleted_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_delete_customer'.tr);
    }
  }

  /// Refresh customers
  Future<void> refreshCustomers() async {
    await loadCustomers();
  }

  /// Generate mock customers for demonstration
  List<Customer> _generateMockCustomers() {
    return [
      Customer(
        id: 'CUST-001',
        customerCode: 'C001',
        name: 'محمد أحمد التجاري',
        email: '<EMAIL>',
        phone: '+966501234567',
        contactPerson: 'محمد أحمد',
        type: 'retail',
        status: 'active',
        address: CustomerAddress(
          street: 'شارع الملك فهد',
          city: 'الرياض',
          state: 'الرياض',
          country: 'المملكة العربية السعودية',
          zipCode: '12345',
        ),
        creditLimit: 10000.0,
        currentBalance: 2500.0,
        paymentTerms: 'net30',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        createdBy: 'USER-001',
        lastOrderDate: DateTime.now().subtract(const Duration(days: 5)),
        totalOrderValue: 15000.0,
        totalOrders: 12,
      ),
      Customer(
        id: 'CUST-002',
        customerCode: 'C002',
        name: 'فاطمة علي للتجارة',
        email: '<EMAIL>',
        phone: '+966507654321',
        contactPerson: 'فاطمة علي',
        type: 'wholesale',
        status: 'active',
        address: CustomerAddress(
          street: 'طريق الملك عبدالعزيز',
          city: 'جدة',
          state: 'مكة المكرمة',
          country: 'المملكة العربية السعودية',
          zipCode: '21589',
        ),
        creditLimit: 25000.0,
        currentBalance: 5200.0,
        paymentTerms: 'net30',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
        createdBy: 'USER-001',
        lastOrderDate: DateTime.now().subtract(const Duration(days: 2)),
        totalOrderValue: 35000.0,
        totalOrders: 28,
      ),
      Customer(
        id: 'CUST-003',
        customerCode: 'C003',
        name: 'عبدالله محمد المؤسسة',
        email: '<EMAIL>',
        phone: '+966509876543',
        contactPerson: 'عبدالله محمد',
        type: 'distributor',
        status: 'inactive',
        address: CustomerAddress(
          street: 'شارع العليا',
          city: 'الدمام',
          state: 'الشرقية',
          country: 'المملكة العربية السعودية',
          zipCode: '31952',
        ),
        creditLimit: 50000.0,
        currentBalance: 0.0,
        paymentTerms: 'net60',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        createdBy: 'USER-001',
        lastOrderDate: DateTime.now().subtract(const Duration(days: 45)),
        totalOrderValue: 75000.0,
        totalOrders: 15,
      ),
    ];
  }
}
