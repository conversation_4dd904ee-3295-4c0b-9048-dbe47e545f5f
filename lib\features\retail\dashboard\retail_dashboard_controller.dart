import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/services/order_service.dart';
import '../../../data/services/customer_service.dart';
import '../../../data/models/user.dart';
import '../../../core/utils/helpers.dart';

/// Controller for retail dashboard screen
class RetailDashboardController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final OrderService _orderService = Get.find<OrderService>();
  final CustomerService _customerService = Get.find<CustomerService>();

  // Observable variables
  final isLoading = false.obs;
  final currentUser = Rxn<User>();
  final todaysOrders = 0.obs;
  final totalInvoices = 0.obs;
  final activeCustomers = 0.obs;
  final pendingReturns = 0.obs;
  final totalSales = 0.0.obs;
  final monthlyTarget = 0.0.obs;

  // Greeting message based on time
  final greeting = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeDashboard();
  }

  /// Initialize dashboard data
  Future<void> _initializeDashboard() async {
    try {
      isLoading.value = true;
      
      // Get current user
      currentUser.value = await _authService.getCurrentUser();
      
      // Set greeting based on time
      _setGreeting();
      
      // Load dashboard statistics
      await _loadDashboardStats();
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_dashboard'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Set greeting message based on current time
  void _setGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      greeting.value = 'good_morning'.tr;
    } else if (hour < 17) {
      greeting.value = 'good_afternoon'.tr;
    } else {
      greeting.value = 'good_evening'.tr;
    }
  }

  /// Load dashboard statistics
  Future<void> _loadDashboardStats() async {
    try {
      // Get today's date
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      // Load statistics (mock data for now)
      todaysOrders.value = 12;
      totalInvoices.value = 45;
      activeCustomers.value = 28;
      pendingReturns.value = 3;
      totalSales.value = 15750.50;
      monthlyTarget.value = 50000.0;

      // TODO: Replace with actual API calls
      // todaysOrders.value = await _orderService.getTodaysOrderCount();
      // totalInvoices.value = await _invoiceService.getTotalInvoiceCount();
      // activeCustomers.value = await _customerService.getActiveCustomerCount();
      // pendingReturns.value = await _returnService.getPendingReturnCount();
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_stats'.tr);
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    await _loadDashboardStats();
    Helpers.showSuccessSnackbar('dashboard_refreshed'.tr);
  }

  /// Navigate to orders screen
  void navigateToOrders() {
    Get.toNamed('/retail/orders');
  }

  /// Navigate to customers screen
  void navigateToCustomers() {
    Get.toNamed('/retail/customers');
  }

  /// Navigate to add order screen
  void navigateToAddOrder() {
    Get.toNamed('/retail/orders/create');
  }

  /// Navigate to add customer screen
  void navigateToAddCustomer() {
    Get.toNamed('/retail/customers/create');
  }

  /// Navigate to schedule visit screen
  void navigateToScheduleVisit() {
    Get.toNamed('/retail/visits/schedule');
  }

  /// Navigate to invoices screen
  void navigateToInvoices() {
    Get.toNamed('/retail/invoices');
  }

  /// Navigate to returns screen
  void navigateToReturns() {
    Get.toNamed('/retail/returns');
  }

  /// Navigate to notifications screen
  void navigateToNotifications() {
    Get.toNamed('/retail/notifications');
  }

  /// Navigate to profile screen
  void navigateToProfile() {
    Get.toNamed('/profile');
  }

  /// Logout user
  Future<void> logout() async {
    try {
      await _authService.logout();
      Get.offAllNamed('/login');
      Helpers.showSuccessSnackbar('logout_success'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('logout_failed'.tr);
    }
  }

  /// Get sales progress percentage
  double get salesProgress {
    if (monthlyTarget.value == 0) return 0.0;
    return (totalSales.value / monthlyTarget.value).clamp(0.0, 1.0);
  }

  /// Get sales progress text
  String get salesProgressText {
    final percentage = (salesProgress * 100).toInt();
    return '$percentage%';
  }

  /// Get remaining target amount
  double get remainingTarget {
    return (monthlyTarget.value - totalSales.value).clamp(0.0, double.infinity);
  }
}
