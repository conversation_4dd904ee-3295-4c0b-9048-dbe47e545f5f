// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Notification _$NotificationFromJson(Map<String, dynamic> json) => Notification(
  id: json['id'] as String,
  title: json['title'] as String,
  message: json['message'] as String,
  type: json['type'] as String,
  entityId: json['entityId'] as String?,
  entityType: json['entityType'] as String?,
  priority: json['priority'] as String? ?? 'medium',
  isRead: json['isRead'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  readAt:
      json['readAt'] == null ? null : DateTime.parse(json['readAt'] as String),
  actionUrl: json['actionUrl'] as String?,
  data: json['data'] as Map<String, dynamic>?,
  imageUrl: json['imageUrl'] as String?,
  icon: json['icon'] as String?,
);

Map<String, dynamic> _$NotificationToJson(Notification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': instance.type,
      'entityId': instance.entityId,
      'entityType': instance.entityType,
      'priority': instance.priority,
      'isRead': instance.isRead,
      'createdAt': instance.createdAt.toIso8601String(),
      'readAt': instance.readAt?.toIso8601String(),
      'actionUrl': instance.actionUrl,
      'data': instance.data,
      'imageUrl': instance.imageUrl,
      'icon': instance.icon,
    };

NotificationSummary _$NotificationSummaryFromJson(Map<String, dynamic> json) =>
    NotificationSummary(
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
      orderCount: (json['orderCount'] as num?)?.toInt() ?? 0,
      visitCount: (json['visitCount'] as num?)?.toInt() ?? 0,
      invoiceCount: (json['invoiceCount'] as num?)?.toInt() ?? 0,
      returnCount: (json['returnCount'] as num?)?.toInt() ?? 0,
      urgentCount: (json['urgentCount'] as num?)?.toInt() ?? 0,
      highPriorityCount: (json['highPriorityCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$NotificationSummaryToJson(
  NotificationSummary instance,
) => <String, dynamic>{
  'totalCount': instance.totalCount,
  'unreadCount': instance.unreadCount,
  'orderCount': instance.orderCount,
  'visitCount': instance.visitCount,
  'invoiceCount': instance.invoiceCount,
  'returnCount': instance.returnCount,
  'urgentCount': instance.urgentCount,
  'highPriorityCount': instance.highPriorityCount,
};
