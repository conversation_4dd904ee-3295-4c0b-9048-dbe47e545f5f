import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/order_service.dart';
import '../../../data/services/customer_service.dart';
import '../../../data/models/order.dart';
import '../../../data/models/customer.dart';
import '../../../core/utils/helpers.dart';

/// Controller for creating new orders
class CreateOrderController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final CustomerService _customerService = Get.find<CustomerService>();

  // Form key
  final formKey = GlobalKey<FormState>();

  // Observable variables
  final isLoading = false.obs;
  final customers = <Customer>[].obs;
  final selectedCustomer = Rxn<Customer>();
  final orderItems = <OrderItem>[].obs;
  final notes = ''.obs;
  final totalAmount = 0.0.obs;

  // Text controllers
  final notesController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadCustomers();
    
    // Listen to order items changes to calculate total
    ever(orderItems, (_) => _calculateTotal());
  }

  @override
  void onClose() {
    notesController.dispose();
    super.onClose();
  }

  /// Load customers for selection
  Future<void> loadCustomers() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      customers.value = _generateMockCustomers();
      
      // TODO: Replace with actual API call
      // customers.value = await _customerService.getCustomers();
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_customers'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Select customer
  void selectCustomer(Customer customer) {
    selectedCustomer.value = customer;
  }

  /// Add product to order
  void addProduct() {
    // For now, add a mock product
    final newItem = OrderItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: 'PROD-${orderItems.length + 1}',
      productName: 'منتج ${orderItems.length + 1}',
      quantity: 1,
      unitPrice: 100.0,
      totalPrice: 100.0,
      notes: '',
    );
    
    orderItems.add(newItem);
  }

  /// Remove product from order
  void removeProduct(int index) {
    if (index >= 0 && index < orderItems.length) {
      orderItems.removeAt(index);
    }
  }

  /// Update product quantity
  void updateProductQuantity(int index, int quantity) {
    if (index >= 0 && index < orderItems.length && quantity > 0) {
      final item = orderItems[index];
      final updatedItem = OrderItem(
        id: item.id,
        productId: item.productId,
        productName: item.productName,
        quantity: quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.unitPrice * quantity,
        notes: item.notes,
      );
      orderItems[index] = updatedItem;
    }
  }

  /// Update product price
  void updateProductPrice(int index, double price) {
    if (index >= 0 && index < orderItems.length && price > 0) {
      final item = orderItems[index];
      final updatedItem = OrderItem(
        id: item.id,
        productId: item.productId,
        productName: item.productName,
        quantity: item.quantity,
        unitPrice: price,
        totalPrice: price * item.quantity,
        notes: item.notes,
      );
      orderItems[index] = updatedItem;
    }
  }

  /// Calculate total amount
  void _calculateTotal() {
    double total = 0.0;
    for (final item in orderItems) {
      total += item.totalPrice;
    }
    totalAmount.value = total;
  }

  /// Validate form
  bool _validateForm() {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (selectedCustomer.value == null) {
      Helpers.showErrorSnackbar('please_select_customer'.tr);
      return false;
    }

    if (orderItems.isEmpty) {
      Helpers.showErrorSnackbar('please_add_products'.tr);
      return false;
    }

    return true;
  }

  /// Create order
  Future<void> createOrder() async {
    if (!_validateForm()) return;

    try {
      isLoading.value = true;

      // Calculate amounts
      final subtotal = totalAmount.value * 0.9; // Assume 10% tax
      final tax = totalAmount.value * 0.1;
      final discount = 0.0;
      final shipping = 0.0;

      final order = Order(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderNumber: 'ORD-${DateTime.now().millisecondsSinceEpoch}',
        customerName: selectedCustomer.value!.name,
        customerId: selectedCustomer.value!.id,
        orderDate: DateTime.now(),
        status: 'pending',
        subtotal: subtotal,
        taxAmount: tax,
        discountAmount: discount,
        shippingAmount: shipping,
        totalAmount: totalAmount.value,
        paymentMethod: 'cash', // Default payment method
        paymentStatus: 'pending',
        items: orderItems.toList(),
        notes: notesController.text.trim(),
        createdBy: 'USER-001', // TODO: Get from auth service
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: Replace with actual API call
      // await _orderService.createOrder(order);

      Helpers.showSuccessSnackbar('order_created_successfully'.tr);
      Get.back(result: true);

    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_create_order'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Generate mock customers for demonstration
  List<Customer> _generateMockCustomers() {
    return [
      Customer(
        id: 'CUST-001',
        customerCode: 'C001',
        name: 'محمد أحمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        contactPerson: 'محمد أحمد',
        type: 'retail',
        status: 'active',
        creditLimit: 10000.0,
        currentBalance: 2500.0,
        paymentTerms: 'net30',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        createdBy: 'USER-001',
        lastOrderDate: DateTime.now().subtract(const Duration(days: 5)),
        totalOrderValue: 15000.0,
        totalOrders: 12,
      ),
      Customer(
        id: 'CUST-002',
        customerCode: 'C002',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '+966507654321',
        contactPerson: 'فاطمة علي',
        type: 'retail',
        status: 'active',
        creditLimit: 15000.0,
        currentBalance: 3200.0,
        paymentTerms: 'net30',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
        createdBy: 'USER-001',
        lastOrderDate: DateTime.now().subtract(const Duration(days: 2)),
        totalOrderValue: 22000.0,
        totalOrders: 18,
      ),
    ];
  }
}
