// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Visit _$VisitFromJson(Map<String, dynamic> json) => Visit(
  id: json['id'] as String,
  visitNumber: json['visitNumber'] as String,
  customerId: json['customerId'] as String,
  customerName: json['customerName'] as String,
  status: json['status'] as String,
  purpose: json['purpose'] as String,
  scheduledDate: DateTime.parse(json['scheduledDate'] as String),
  startTime:
      json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
  endTime:
      json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
  notes: json['notes'] as String?,
  outcome: json['outcome'] as String?,
  attachments:
      (json['attachments'] as List<dynamic>?)?.map((e) => e as String).toList(),
  location:
      json['location'] == null
          ? null
          : VisitLocation.fromJson(json['location'] as Map<String, dynamic>),
  nextAction: json['nextAction'] as String?,
  nextVisitDate:
      json['nextVisitDate'] == null
          ? null
          : DateTime.parse(json['nextVisitDate'] as String),
  assignedTo: json['assignedTo'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  cancelReason: json['cancelReason'] as String?,
  cancelledAt:
      json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
);

Map<String, dynamic> _$VisitToJson(Visit instance) => <String, dynamic>{
  'id': instance.id,
  'visitNumber': instance.visitNumber,
  'customerId': instance.customerId,
  'customerName': instance.customerName,
  'status': instance.status,
  'purpose': instance.purpose,
  'scheduledDate': instance.scheduledDate.toIso8601String(),
  'startTime': instance.startTime?.toIso8601String(),
  'endTime': instance.endTime?.toIso8601String(),
  'notes': instance.notes,
  'outcome': instance.outcome,
  'attachments': instance.attachments,
  'location': instance.location,
  'nextAction': instance.nextAction,
  'nextVisitDate': instance.nextVisitDate?.toIso8601String(),
  'assignedTo': instance.assignedTo,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'cancelReason': instance.cancelReason,
  'cancelledAt': instance.cancelledAt?.toIso8601String(),
};

VisitLocation _$VisitLocationFromJson(Map<String, dynamic> json) =>
    VisitLocation(
      address: json['address'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      checkedInAt:
          json['checkedInAt'] == null
              ? null
              : DateTime.parse(json['checkedInAt'] as String),
      checkedOutAt:
          json['checkedOutAt'] == null
              ? null
              : DateTime.parse(json['checkedOutAt'] as String),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$VisitLocationToJson(VisitLocation instance) =>
    <String, dynamic>{
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'checkedInAt': instance.checkedInAt?.toIso8601String(),
      'checkedOutAt': instance.checkedOutAt?.toIso8601String(),
      'accuracy': instance.accuracy,
    };
