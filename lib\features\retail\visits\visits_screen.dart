import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'visits_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/cards/visit_card.dart';

/// Visits list screen for retail representatives
class VisitsScreen extends GetView<VisitsController> {
  const VisitsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            // Search and filters
            _buildSearchAndFilters(),
            
            // Visits list
            Expanded(
              child: _buildVisitsList(),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('visits'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: controller.clearFilters,
          icon: const Icon(Icons.clear_all),
          tooltip: 'clear_filters'.tr,
        ),
        IconButton(
          onPressed: controller.refreshVisits,
          icon: const Icon(Icons.refresh),
          tooltip: 'refresh'.tr,
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field
          CustomTextField(
            hintText: 'search_visits'.tr,
            prefixIcon: Icons.search,
            onChanged: controller.updateSearchQuery,
            isRTL: true,
          ),
          
          SizedBox(height: 12.h),
          
          // Filter chips
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  label: 'status'.tr,
                  value: controller.selectedStatus,
                  options: controller.statusOptions,
                  onChanged: controller.updateStatusFilter,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildFilterChip(
                  label: 'date_range'.tr,
                  value: controller.selectedDateRange,
                  options: controller.dateRangeOptions,
                  onChanged: controller.updateDateRangeFilter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required RxString value,
    required List<String> options,
    required Function(String) onChanged,
  }) {
    return Obx(() => Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value.value,
          isExpanded: true,
          hint: Text(label),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(
                option == 'all' ? 'all'.tr : option.tr,
                style: AppTextStyles.bodySmall,
              ),
            );
          }).toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ),
    ));
  }

  Widget _buildVisitsList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (controller.filteredVisits.isEmpty) {
        return _buildEmptyState();
      }

      return RefreshIndicator(
        onRefresh: controller.refreshVisits,
        child: ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: controller.filteredVisits.length,
          itemBuilder: (context, index) {
            final visit = controller.filteredVisits[index];
            return VisitCard(
              visit: visit,
              onTap: () => controller.navigateToVisitDetails(visit.id),
              onStart: () => controller.startVisit(visit.id),
              onComplete: () => _showCompleteVisitDialog(visit.id),
              onCancel: () => _showCancelVisitDialog(visit.id),
              onEdit: () => controller.navigateToEditVisit(visit.id),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_available,
            size: 64.sp,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            'no_visits_found'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'try_adjusting_filters'.tr,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: controller.navigateToScheduleVisit,
            icon: const Icon(Icons.add),
            label: Text('schedule_visit'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: controller.navigateToScheduleVisit,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      child: const Icon(Icons.add),
    );
  }

  void _showCompleteVisitDialog(String visitId) {
    final notesController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('complete_visit'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('add_visit_notes'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: notesController,
                hintText: 'enter_visit_notes'.tr,
                maxLines: 3,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('cancel'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                controller.completeVisit(visitId, notesController.text.trim());
              },
              child: Text('complete'.tr),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelVisitDialog(String visitId) {
    final reasonController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('cancel_visit'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('cancel_visit_reason'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: reasonController,
                hintText: 'enter_cancel_reason'.tr,
                maxLines: 2,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('no'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                controller.cancelVisit(visitId, reasonController.text.trim());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: Text('yes'.tr),
            ),
          ],
        ),
      ),
    );
  }
}
