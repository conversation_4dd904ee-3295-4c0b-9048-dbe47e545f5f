import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/visit.dart';
import '../../../core/utils/helpers.dart';

/// Controller for visits management
class VisitsController extends GetxController {

  // Observable variables
  final isLoading = false.obs;
  final visits = <Visit>[].obs;
  final filteredVisits = <Visit>[].obs;
  final searchQuery = ''.obs;
  final selectedStatus = 'all'.obs;
  final selectedDateRange = 'all'.obs;

  // Filter options
  final statusOptions = [
    'all',
    'scheduled',
    'in_progress',
    'completed',
    'cancelled',
    'missed'
  ].obs;

  final dateRangeOptions = [
    'all',
    'today',
    'tomorrow',
    'this_week',
    'next_week',
    'this_month'
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadVisits();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedDateRange, (_) => _applyFilters());
  }

  /// Load visits from service
  Future<void> loadVisits() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      visits.value = _generateMockVisits();
      
      // TODO: Replace with actual API call
      // visits.value = await _visitService.getVisits();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_visits'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = visits.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((visit) {
        return visit.customerName.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               visit.purpose.toLowerCase().contains(searchQuery.value.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      filtered = filtered.where((visit) => visit.status == selectedStatus.value).toList();
    }

    // Apply date range filter
    if (selectedDateRange.value != 'all') {
      final now = DateTime.now();
      DateTime? startDate;
      DateTime? endDate;

      switch (selectedDateRange.value) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          endDate = startDate.add(const Duration(days: 1));
          break;
        case 'tomorrow':
          startDate = DateTime(now.year, now.month, now.day + 1);
          endDate = startDate.add(const Duration(days: 1));
          break;
        case 'this_week':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          endDate = startDate.add(const Duration(days: 7));
          break;
        case 'next_week':
          startDate = now.add(Duration(days: 7 - now.weekday + 1));
          endDate = startDate.add(const Duration(days: 7));
          break;
        case 'this_month':
          startDate = DateTime(now.year, now.month, 1);
          endDate = DateTime(now.year, now.month + 1, 1);
          break;
      }

      if (startDate != null && endDate != null) {
        filtered = filtered.where((visit) {
          return visit.scheduledDate.isAfter(startDate!) && 
                 visit.scheduledDate.isBefore(endDate!);
        }).toList();
      }
    }

    // Sort by scheduled date (nearest first)
    filtered.sort((a, b) => a.scheduledDate.compareTo(b.scheduledDate));

    filteredVisits.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update date range filter
  void updateDateRangeFilter(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
    selectedDateRange.value = 'all';
  }

  /// Navigate to visit details
  void navigateToVisitDetails(String visitId) {
    Get.toNamed('/retail/visits/$visitId');
  }

  /// Navigate to schedule visit
  void navigateToScheduleVisit() {
    Get.toNamed('/retail/visits/schedule');
  }

  /// Navigate to edit visit
  void navigateToEditVisit(String visitId) {
    Get.toNamed('/retail/visits/$visitId/edit');
  }

  /// Start visit (mark as in progress)
  Future<void> startVisit(String visitId) async {
    try {
      // TODO: Call API to update visit status
      // await _visitService.updateVisitStatus(visitId, 'in_progress');

      // Update local list
      final visitIndex = visits.indexWhere((visit) => visit.id == visitId);
      if (visitIndex != -1) {
        visits[visitIndex] = visits[visitIndex].copyWith(
          status: 'in_progress',
          startTime: DateTime.now(),
        );
        _applyFilters();
      }

      Helpers.showSuccessSnackbar('visit_started_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_start_visit'.tr);
    }
  }

  /// Complete visit
  Future<void> completeVisit(String visitId, String notes) async {
    try {
      // TODO: Call API to complete visit
      // await _visitService.completeVisit(visitId, notes);

      // Update local list
      final visitIndex = visits.indexWhere((visit) => visit.id == visitId);
      if (visitIndex != -1) {
        visits[visitIndex] = visits[visitIndex].copyWith(
          status: 'completed',
          endTime: DateTime.now(),
          notes: notes,
          outcome: 'successful',
        );
        _applyFilters();
      }

      Helpers.showSuccessSnackbar('visit_completed_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_complete_visit'.tr);
    }
  }

  /// Cancel visit
  Future<void> cancelVisit(String visitId, String reason) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_cancel'.tr),
          content: Text('confirm_cancel_visit'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('no'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('yes'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to cancel visit
        // await _visitService.cancelVisit(visitId, reason);
        
        // Update local list
        final visitIndex = visits.indexWhere((visit) => visit.id == visitId);
        if (visitIndex != -1) {
          visits[visitIndex] = visits[visitIndex].copyWith(
            status: 'cancelled',
            notes: reason,
          );
          _applyFilters();
        }
        
        Helpers.showSuccessSnackbar('visit_cancelled_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_cancel_visit'.tr);
    }
  }

  /// Refresh visits
  Future<void> refreshVisits() async {
    await loadVisits();
  }

  /// Generate mock visits for demonstration
  List<Visit> _generateMockVisits() {
    final now = DateTime.now();
    return [
      Visit(
        id: '1',
        visitNumber: '001',
        customerId: 'CUST-001',
        customerName: 'محمد أحمد التجاري',
        purpose: 'sales_call',
        scheduledDate: now.add(const Duration(hours: 2)),
        status: 'scheduled',
        location: VisitLocation(
          address: 'شارع الملك فهد، الرياض',
          latitude: 24.7136,
          longitude: 46.6753,
        ),
        assignedTo: 'USER-001',
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
      ),
      Visit(
        id: '2',
        visitNumber: '002',
        customerId: 'CUST-002',
        customerName: 'فاطمة علي للتجارة',
        purpose: 'follow_up',
        scheduledDate: now.add(const Duration(days: 1)),
        status: 'scheduled',
        location: VisitLocation(
          address: 'طريق الملك عبدالعزيز، جدة',
          latitude: 21.3891,
          longitude: 39.8579,
        ),
        assignedTo: 'USER-001',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Visit(
        id: '3',
        visitNumber: '003',
        customerId: 'CUST-003',
        customerName: 'عبدالله محمد المؤسسة',
        purpose: 'collection',
        scheduledDate: now.subtract(const Duration(hours: 3)),
        status: 'completed',
        location: VisitLocation(
          address: 'شارع العليا، الدمام',
          latitude: 26.4207,
          longitude: 50.0888,
        ),
        startTime: now.subtract(const Duration(hours: 3)),
        endTime: now.subtract(const Duration(hours: 2)),
        notes: 'تم مراجعة الحساب وتحديث البيانات',
        outcome: 'successful',
        assignedTo: 'USER-001',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(hours: 2)),
      ),
    ];
  }
}
