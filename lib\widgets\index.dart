/// Al Ameen Sales App - Reusable Components Library
///
/// This file exports all reusable UI components for easy importing
/// throughout the application. All components are designed with
/// Arabic RTL layout support and consistent theming.
library;

// Cards
export 'cards/customer_card.dart';
export 'cards/order_card.dart';
export 'cards/visit_card.dart';
export 'cards/invoice_card.dart';
export 'cards/return_card.dart';
export 'cards/notification_card.dart';
export 'cards/profile_card.dart';

// Forms
export 'forms/custom_text_field.dart';
export 'forms/custom_dropdown.dart';
export 'forms/custom_button.dart';

// Navigation
export 'navigation/custom_bottom_navigation.dart';
export 'navigation/custom_app_bar.dart';

// Buttons
export 'buttons/action_button.dart';
export 'buttons/floating_action_button.dart';

// Utilities
export 'utils/loading_widget.dart';
export 'utils/empty_state_widget.dart';
export 'utils/error_widget.dart';

/// Component Categories:
/// 
/// 1. Cards (7 components):
///    - CustomerCard: Display customer information with status and actions
///    - OrderCard: Display order information with status tracking
///    - VisitCard: Display visit information with scheduling
///    - InvoiceCard: Display invoice information with payment tracking
///    - ReturnCard: Display return information with approval workflow
///    - NotificationCard: Display notification information with read/unread status
///    - ProfileCard: Display user profile information with statistics
/// 
/// 2. Forms (3 components):
///    - CustomTextField: Standardized text input with RTL support
///    - CustomDropdown: Standardized dropdown with RTL support
///    - CustomButton: Standardized button with loading states
/// 
/// 3. Navigation (2 components):
///    - CustomBottomNavigation: Bottom navigation with Arabic labels
///    - CustomAppBar: Standardized app bar with RTL support
/// 
/// 4. Buttons (2 components):
///    - ActionButton: Standardized action button with consistent styling
///    - FloatingActionButton: Customized FAB with theme integration
/// 
/// 5. Utilities (3 components):
///    - LoadingWidget: Standardized loading indicator
///    - EmptyStateWidget: Standardized empty state display
///    - ErrorWidget: Standardized error state display
/// 
/// Usage Examples:
/// 
/// ```dart
/// import 'package:al_ameen_app/widgets/index.dart';
/// 
/// // Using a card component
/// CustomerCard(
///   customer: customer,
///   onTap: () => controller.navigateToDetails(customer.id),
/// )
/// 
/// // Using a form component
/// CustomTextField(
///   labelText: 'customer_name'.tr,
///   prefixIcon: Icons.person,
///   isRTL: true,
/// )
/// 
/// // Using a utility component
/// LoadingWidget(
///   message: 'loading_customers'.tr,
/// )
/// ```
/// 
/// Design Principles:
/// 
/// 1. RTL Support: All components support Arabic RTL text direction
/// 2. Consistent Theming: All components use the app's color scheme
/// 3. Accessibility: Proper semantic labels and touch targets
/// 4. Responsive Design: Components adapt to different screen sizes
/// 5. Internationalization: All text uses translation keys
/// 
/// Performance Guidelines:
/// 
/// 1. Use const constructors where possible
/// 2. Avoid unnecessary rebuilds
/// 3. Extract complex logic to controllers
/// 4. Use efficient list builders for large datasets
/// 5. Implement proper disposal of resources
/// 
/// Customization:
/// 
/// All components accept customization parameters while maintaining
/// consistency with the app's design system. Common customization
/// options include:
/// 
/// - Colors (while respecting theme colors)
/// - Sizes (using responsive scaling)
/// - Callbacks for user interactions
/// - Content customization through parameters
/// 
/// For more detailed documentation, see:
/// - lib/widgets/README.md
/// - Individual component files
/// - Design system documentation
