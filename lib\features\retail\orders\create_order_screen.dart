import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'create_order_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/common/custom_button.dart';
import '../../../core/utils/format_utils.dart';

/// Create order screen for retail representatives
class CreateOrderScreen extends GetView<CreateOrderController> {
  const CreateOrderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Form(
          key: controller.formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Customer selection
                      _buildCustomerSelection(),
                      
                      SizedBox(height: 24.h),
                      
                      // Order items
                      _buildOrderItems(),
                      
                      SizedBox(height: 24.h),
                      
                      // Notes
                      _buildNotes(),
                      
                      SizedBox(height: 24.h),
                      
                      // Total amount
                      _buildTotalAmount(),
                    ],
                  ),
                ),
              ),
              
              // Create button
              _buildCreateButton(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('create_order'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
    );
  }

  Widget _buildCustomerSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'select_customer'.tr,
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        
        SizedBox(height: 12.h),
        
        Obx(() => Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.borderLight),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton(
              value: controller.selectedCustomer.value,
              isExpanded: true,
              hint: Text('choose_customer'.tr),
              items: controller.customers.map((customer) {
                return DropdownMenuItem(
                  value: customer,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customer.name,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        customer.customerCode,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (customer) {
                if (customer != null) {
                  controller.selectCustomer(customer);
                }
              },
            ),
          ),
        )),
      ],
    );
  }

  Widget _buildOrderItems() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'order_items'.tr,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: controller.addProduct,
              icon: const Icon(Icons.add, size: 18),
              label: Text('add_product'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),
        
        Obx(() {
          if (controller.orderItems.isEmpty) {
            return Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderLight),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: 48.sp,
                      color: AppColors.onSurfaceVariant,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'no_products_added'.tr,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.orderItems.length,
            itemBuilder: (context, index) {
              final item = controller.orderItems[index];
              return _buildOrderItemCard(item, index);
            },
          );
        }),
      ],
    );
  }

  Widget _buildOrderItemCard(item, int index) {
    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.productName,
                    style: AppTextStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => controller.removeProduct(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  iconSize: 20.sp,
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('quantity'.tr, style: AppTextStyles.labelSmall),
                      Text(item.quantity.toString(), style: AppTextStyles.bodyMedium),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('unit_price'.tr, style: AppTextStyles.labelSmall),
                      Text(
                        FormatUtils.formatCurrency(item.unitPrice),
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('total'.tr, style: AppTextStyles.labelSmall),
                      Text(
                        FormatUtils.formatCurrency(item.totalPrice),
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'notes'.tr,
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        
        SizedBox(height: 12.h),
        
        CustomTextField(
          controller: controller.notesController,
          hintText: 'enter_order_notes'.tr,
          maxLines: 3,
          isRTL: true,
        ),
      ],
    );
  }

  Widget _buildTotalAmount() {
    return Obx(() => Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primaryContainer,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'total_amount'.tr,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
          ),
          Text(
            FormatUtils.formatCurrency(controller.totalAmount.value),
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildCreateButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Obx(() => CustomButton(
          text: 'confirm_order'.tr,
          onPressed: controller.isLoading.value ? null : controller.createOrder,
          isLoading: controller.isLoading.value,
          width: double.infinity,
        )),
      ),
    );
  }
}
