import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'retail_dashboard_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/cards/summary_card.dart';
import '../../../widgets/cards/quick_action_card.dart';

/// Retail dashboard screen for Al Ameen Sales App
class RetailDashboardScreen extends GetView<RetailDashboardController> {
  const RetailDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: controller.refreshDashboard,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  _buildHeader(),
                  
                  Si<PERSON><PERSON><PERSON>(height: 24.h),
                  
                  // Summary Cards
                  _buildSummaryCards(),
                  
                  SizedBox(height: 24.h),
                  
                  // Sales Progress
                  _buildSalesProgress(),
                  
                  SizedBox(height: 24.h),
                  
                  // Quick Actions
                  _buildQuickActions(),
                  
                  SizedBox(height: 24.h),
                  
                  // Recent Activities (placeholder for now)
                  _buildRecentActivities(),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomNavigation(),
      ),
    );
  }

  Widget _buildHeader() {
    return Obx(() => Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${controller.greeting.value}،',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                controller.currentUser.value?.firstName ?? 'مندوب المبيعات',
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
        ),
        
        // Profile and notifications
        Row(
          children: [
            IconButton(
              onPressed: controller.navigateToNotifications,
              icon: const Icon(Icons.notifications_outlined),
              color: AppColors.onSurfaceVariant,
            ),
            SizedBox(width: 8.w),
            GestureDetector(
              onTap: controller.navigateToProfile,
              child: CircleAvatar(
                radius: 20.r,
                backgroundColor: AppColors.primary,
                child: Text(
                  controller.currentUser.value?.firstName.substring(0, 1) ?? 'م',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    ));
  }

  Widget _buildSummaryCards() {
    return Obx(() => GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.3,
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      children: [
        SummaryCard(
          title: 'todays_orders'.tr,
          value: controller.todaysOrders.value.toString(),
          icon: Icons.shopping_cart_outlined,
          iconColor: AppColors.primary,
          onTap: controller.navigateToOrders,
        ),
        SummaryCard(
          title: 'total_invoices'.tr,
          value: controller.totalInvoices.value.toString(),
          icon: Icons.receipt_long_outlined,
          iconColor: AppColors.secondary,
          onTap: controller.navigateToInvoices,
        ),
        SummaryCard(
          title: 'active_customers'.tr,
          value: controller.activeCustomers.value.toString(),
          icon: Icons.people_outline,
          iconColor: AppColors.info,
          onTap: controller.navigateToCustomers,
        ),
        SummaryCard(
          title: 'pending_returns'.tr,
          value: controller.pendingReturns.value.toString(),
          icon: Icons.assignment_return_outlined,
          iconColor: AppColors.warning,
          onTap: controller.navigateToReturns,
        ),
      ],
    ));
  }

  Widget _buildSalesProgress() {
    return Obx(() => ProgressSummaryCard(
      title: 'monthly_sales_target'.tr,
      value: '${controller.totalSales.value.toStringAsFixed(0)} ر.س',
      icon: Icons.trending_up,
      progress: controller.salesProgress,
      progressColor: AppColors.success,
      iconColor: AppColors.success,
      progressText: controller.salesProgressText,
    ));
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'quick_actions'.tr,
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        QuickActionsGrid(
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          actions: [
            QuickActionCard(
              title: 'add_order'.tr,
              icon: Icons.add_shopping_cart,
              iconColor: AppColors.primary,
              onTap: controller.navigateToAddOrder,
            ),
            QuickActionCard(
              title: 'add_customer'.tr,
              icon: Icons.person_add,
              iconColor: AppColors.secondary,
              onTap: controller.navigateToAddCustomer,
            ),
            QuickActionCard(
              title: 'add_visit'.tr,
              icon: Icons.event_available,
              iconColor: AppColors.accent,
              onTap: controller.navigateToScheduleVisit,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'recent_activities'.tr,
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        Card(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                Icon(
                  Icons.timeline,
                  size: 48.sp,
                  color: AppColors.onSurfaceVariant,
                ),
                SizedBox(height: 12.h),
                Text(
                  'no_recent_activities'.tr,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                icon: Icons.dashboard,
                label: 'dashboard'.tr,
                isActive: true,
                onTap: () {},
              ),
              _buildNavItem(
                icon: Icons.shopping_cart_outlined,
                label: 'orders'.tr,
                onTap: controller.navigateToOrders,
              ),
              _buildNavItem(
                icon: Icons.people_outline,
                label: 'customers'.tr,
                onTap: controller.navigateToCustomers,
              ),
              _buildNavItem(
                icon: Icons.receipt_long_outlined,
                label: 'invoices'.tr,
                onTap: controller.navigateToInvoices,
              ),
              _buildNavItem(
                icon: Icons.account_circle_outlined,
                label: 'profile'.tr,
                onTap: controller.navigateToProfile,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive ? AppColors.primary : AppColors.onSurfaceVariant,
            size: 24.sp,
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(
              color: isActive ? AppColors.primary : AppColors.onSurfaceVariant,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
