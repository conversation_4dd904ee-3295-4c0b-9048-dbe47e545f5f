import 'package:get/get.dart';
import 'login_controller.dart';
import '../../../data/services/auth_service.dart';

/// Login screen binding for dependency injection
class LoginBinding extends Bindings {
  @override
  void dependencies() {
    // Register AuthService if not already registered
    if (!Get.isRegistered<AuthService>()) {
      Get.put<AuthService>(AuthService(), permanent: true);
    }
    
    // Register LoginController
    Get.lazyPut<LoginController>(() => LoginController());
  }
}
