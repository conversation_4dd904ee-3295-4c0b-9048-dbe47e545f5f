import 'package:get/get.dart';
import '../data/services/auth_service.dart';
import '../data/services/customer_service.dart';
import '../data/services/order_service.dart';

/// Initial binding for global dependency injection
class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Core services (permanent)
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<CustomerService>(CustomerService(), permanent: true);
    Get.put<OrderService>(OrderService(), permanent: true);
    
    // Add other services as they are created
    // Get.put<VisitService>(VisitService(), permanent: true);
    // Get.put<InvoiceService>(InvoiceService(), permanent: true);
    // Get.put<ReturnService>(ReturnService(), permanent: true);
    // Get.put<NotificationService>(NotificationService(), permanent: true);
  }
}
