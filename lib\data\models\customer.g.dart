// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
  id: json['id'] as String,
  customerCode: json['customerCode'] as String,
  name: json['name'] as String,
  email: json['email'] as String?,
  phone: json['phone'] as String?,
  contactPerson: json['contactPerson'] as String?,
  type: json['type'] as String,
  status: json['status'] as String,
  address:
      json['address'] == null
          ? null
          : CustomerAddress.fromJson(json['address'] as Map<String, dynamic>),
  notes: json['notes'] as String?,
  creditLimit: (json['creditLimit'] as num?)?.toDouble() ?? 0.0,
  currentBalance: (json['currentBalance'] as num?)?.toDouble() ?? 0.0,
  paymentTerms: json['paymentTerms'] as String? ?? 'cash',
  taxId: json['taxId'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  createdBy: json['createdBy'] as String,
  lastOrderDate:
      json['lastOrderDate'] == null
          ? null
          : DateTime.parse(json['lastOrderDate'] as String),
  totalOrderValue: (json['totalOrderValue'] as num?)?.toDouble() ?? 0.0,
  totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
  'id': instance.id,
  'customerCode': instance.customerCode,
  'name': instance.name,
  'email': instance.email,
  'phone': instance.phone,
  'contactPerson': instance.contactPerson,
  'type': instance.type,
  'status': instance.status,
  'address': instance.address,
  'notes': instance.notes,
  'creditLimit': instance.creditLimit,
  'currentBalance': instance.currentBalance,
  'paymentTerms': instance.paymentTerms,
  'taxId': instance.taxId,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'createdBy': instance.createdBy,
  'lastOrderDate': instance.lastOrderDate?.toIso8601String(),
  'totalOrderValue': instance.totalOrderValue,
  'totalOrders': instance.totalOrders,
};

CustomerAddress _$CustomerAddressFromJson(Map<String, dynamic> json) =>
    CustomerAddress(
      street: json['street'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      zipCode: json['zipCode'] as String?,
      country: json['country'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      isDefault: json['isDefault'] as bool? ?? true,
    );

Map<String, dynamic> _$CustomerAddressToJson(CustomerAddress instance) =>
    <String, dynamic>{
      'street': instance.street,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'isDefault': instance.isDefault,
    };

CustomerStats _$CustomerStatsFromJson(Map<String, dynamic> json) =>
    CustomerStats(
      customerId: json['customerId'] as String,
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      totalOrderValue: (json['totalOrderValue'] as num?)?.toDouble() ?? 0.0,
      averageOrderValue: (json['averageOrderValue'] as num?)?.toDouble() ?? 0.0,
      totalVisits: (json['totalVisits'] as num?)?.toInt() ?? 0,
      lastOrderDate:
          json['lastOrderDate'] == null
              ? null
              : DateTime.parse(json['lastOrderDate'] as String),
      lastVisitDate:
          json['lastVisitDate'] == null
              ? null
              : DateTime.parse(json['lastVisitDate'] as String),
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
      topProducts:
          (json['topProducts'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      yearToDateValue: (json['yearToDateValue'] as num?)?.toDouble() ?? 0.0,
      lastYearValue: (json['lastYearValue'] as num?)?.toDouble() ?? 0.0,
      growthPercentage: (json['growthPercentage'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$CustomerStatsToJson(CustomerStats instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'totalOrders': instance.totalOrders,
      'totalOrderValue': instance.totalOrderValue,
      'averageOrderValue': instance.averageOrderValue,
      'totalVisits': instance.totalVisits,
      'lastOrderDate': instance.lastOrderDate?.toIso8601String(),
      'lastVisitDate': instance.lastVisitDate?.toIso8601String(),
      'preferredPaymentMethod': instance.preferredPaymentMethod,
      'topProducts': instance.topProducts,
      'yearToDateValue': instance.yearToDateValue,
      'lastYearValue': instance.lastYearValue,
      'growthPercentage': instance.growthPercentage,
    };
