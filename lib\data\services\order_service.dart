import 'package:get/get.dart';
import '../models/order.dart';
import '../../core/config/app_config.dart';
import '../../core/constants/api_endpoints.dart';
import '../../core/exceptions/app_exception.dart';

/// Order service for Al Ameen Sales App
class OrderService extends GetConnect {
  @override
  void onInit() {
    super.onInit();
    httpClient.baseUrl = AppConfig.baseUrl;
    httpClient.timeout = Duration(milliseconds: AppConfig.connectTimeout);
    
    httpClient.addRequestModifier<dynamic>((request) async {
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      return request;
    });
  }

  /// Get orders with pagination and filters
  Future<List<Order>> getOrders({
    int page = 1,
    int limit = 20,
    OrderFilters? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        ...?filters?.toQueryParams(),
      };

      final response = await get(ApiEndpoints.orders, query: queryParams);

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Order.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch orders: ${e.toString()}');
    }
  }

  /// Get order by ID
  Future<Order> getOrder(String orderId) async {
    try {
      final response = await get(ApiEndpoints.getOrderDetails(orderId));

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Order.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch order: ${e.toString()}');
    }
  }

  /// Create new order
  Future<Order> createOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await post(ApiEndpoints.createOrder, orderData);

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Order.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to create order: ${e.toString()}');
    }
  }

  /// Update order
  Future<Order> updateOrder(String orderId, Map<String, dynamic> orderData) async {
    try {
      final response = await put(ApiEndpoints.getOrderDetails(orderId), orderData);

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Order.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to update order: ${e.toString()}');
    }
  }

  /// Update order status
  Future<Order> updateOrderStatus(String orderId, String status) async {
    try {
      final response = await patch(
        '${ApiEndpoints.getOrderDetails(orderId)}/status',
        {'status': status},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Order.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to update order status: ${e.toString()}');
    }
  }

  /// Cancel order
  Future<Order> cancelOrder(String orderId, String reason) async {
    try {
      final response = await patch(
        '${ApiEndpoints.getOrderDetails(orderId)}/cancel',
        {'reason': reason},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      return Order.fromJson(data);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to cancel order: ${e.toString()}');
    }
  }

  /// Get orders by customer
  Future<List<Order>> getOrdersByCustomer(String customerId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await get(
        ApiEndpoints.getOrdersByCustomer(customerId),
        query: {'page': page, 'limit': limit},
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'] as List;
      return data.map((json) => Order.fromJson(json)).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch customer orders: ${e.toString()}');
    }
  }

  /// Get order statuses
  Future<List<String>> getOrderStatuses() async {
    try {
      final response = await get(ApiEndpoints.orderStatuses);

      if (response.hasError) {
        throw _handleError(response);
      }

      return List<String>.from(response.body['data']);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to fetch order statuses: ${e.toString()}');
    }
  }

  /// Generate order report
  Future<String> generateOrderReport({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'pdf',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'format': format,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final response = await get(ApiEndpoints.orderReport, query: queryParams);

      if (response.hasError) {
        throw _handleError(response);
      }

      return response.body['data']['download_url'];
    } catch (e) {
      if (e is AppException) rethrow;
      throw ApiException('Failed to generate order report: ${e.toString()}');
    }
  }

  /// Handle API errors
  AppException _handleError(Response response) {
    final statusCode = response.statusCode ?? 0;
    final body = response.body;
    
    String message = 'An error occurred';
    String? code;
    
    if (body is Map<String, dynamic>) {
      message = body['message'] ?? message;
      code = body['code'];
    }
    
    return ExceptionFactory.createFromHttpStatus(
      statusCode: statusCode,
      message: message,
      code: code,
      details: body,
    );
  }
}
