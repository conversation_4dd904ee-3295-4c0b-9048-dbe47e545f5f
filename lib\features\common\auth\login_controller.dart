import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/user.dart';
import '../../../routes/app_routes.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/utils/validators.dart';
import '../../../core/exceptions/app_exception.dart';

/// Login controller for Al Ameen Sales App
class LoginController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Form controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Reactive variables
  final isLoading = false.obs;
  final isPasswordVisible = false.obs;
  final rememberMe = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadSavedCredentials();
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  /// Load saved credentials if remember me was enabled
  Future<void> _loadSavedCredentials() async {
    // Implementation would load from secure storage
    // For now, just a placeholder
  }

  /// Validate email field
  String? validateEmail(String? value) {
    return Validators.email(value);
  }

  /// Validate password field
  String? validatePassword(String? value) {
    return Validators.password(value);
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// Toggle remember me
  void toggleRememberMe(bool? value) {
    rememberMe.value = value ?? false;
  }

  /// Perform login
  Future<void> login() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isLoading.value = true;
      Helpers.hideKeyboard();

      final user = await _authService.login(
        emailController.text.trim(),
        passwordController.text,
      );

      // Save credentials if remember me is enabled
      if (rememberMe.value) {
        await _saveCredentials();
      }

      // Show success message
      Helpers.showSuccessSnackbar('Welcome back, ${user.firstName}!');

      // Navigate based on user role
      _navigateBasedOnRole(user);
    } on AuthException catch (e) {
      Helpers.showErrorSnackbar(e.userFriendlyMessage);
    } on NetworkException catch (e) {
      Helpers.showErrorSnackbar(e.userFriendlyMessage);
    } catch (e) {
      Helpers.showErrorSnackbar('Login failed. Please try again.');
    } finally {
      isLoading.value = false;
    }
  }

  /// Navigate based on user role
  void _navigateBasedOnRole(User user) {
    if (user.role == AppConstants.retailRole) {
      Get.offAllNamed(AppRoutes.retailDashboard);
    } else if (user.role == AppConstants.wholesaleRole) {
      Get.offAllNamed(AppRoutes.wholesaleDashboard);
    } else {
      Helpers.showErrorSnackbar('Invalid user role. Please contact support.');
    }
  }

  /// Save credentials for remember me
  Future<void> _saveCredentials() async {
    // Implementation would save to secure storage
    // For now, just a placeholder
  }

  /// Handle forgot password
  Future<void> forgotPassword() async {
    final email = emailController.text.trim();
    
    if (email.isEmpty) {
      Helpers.showWarningSnackbar('Please enter your email address first');
      return;
    }

    if (Validators.email(email) != null) {
      Helpers.showWarningSnackbar('Please enter a valid email address');
      return;
    }

    try {
      isLoading.value = true;

      await _authService.forgotPassword(email);
      
      Helpers.showSuccessSnackbar(
        'Password reset instructions have been sent to your email',
      );
    } on AuthException catch (e) {
      Helpers.showErrorSnackbar(e.userFriendlyMessage);
    } on NetworkException catch (e) {
      Helpers.showErrorSnackbar(e.userFriendlyMessage);
    } catch (e) {
      Helpers.showErrorSnackbar('Failed to send reset instructions. Please try again.');
    } finally {
      isLoading.value = false;
    }
  }

  /// Quick login for demo purposes (development only)
  Future<void> quickLoginRetail() async {
    if (!Get.context!.mounted) return;
    
    emailController.text = '<EMAIL>';
    passwordController.text = 'password123';
    await login();
  }

  /// Quick login for demo purposes (development only)
  Future<void> quickLoginWholesale() async {
    if (!Get.context!.mounted) return;
    
    emailController.text = '<EMAIL>';
    passwordController.text = 'password123';
    await login();
  }

  /// Clear form
  void clearForm() {
    emailController.clear();
    passwordController.clear();
    isPasswordVisible.value = false;
    rememberMe.value = false;
    formKey.currentState?.reset();
  }
}
