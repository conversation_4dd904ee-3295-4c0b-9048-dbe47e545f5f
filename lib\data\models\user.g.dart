// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  email: json['email'] as String,
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  phone: json['phone'] as String?,
  avatar: json['avatar'] as String?,
  role: json['role'] as String,
  status: json['status'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  lastLoginAt:
      json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
  profile:
      json['profile'] == null
          ? null
          : UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
  settings:
      json['settings'] == null
          ? null
          : UserSettings.fromJson(json['settings'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'email': instance.email,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'phone': instance.phone,
  'avatar': instance.avatar,
  'role': instance.role,
  'status': instance.status,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
  'profile': instance.profile,
  'settings': instance.settings,
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  department: json['department'] as String?,
  position: json['position'] as String?,
  manager: json['manager'] as String?,
  territory: json['territory'] as String?,
  region: json['region'] as String?,
  hireDate:
      json['hireDate'] == null
          ? null
          : DateTime.parse(json['hireDate'] as String),
  employeeId: json['employeeId'] as String?,
  address: json['address'] as String?,
  city: json['city'] as String?,
  state: json['state'] as String?,
  zipCode: json['zipCode'] as String?,
  country: json['country'] as String?,
  bio: json['bio'] as String?,
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'department': instance.department,
      'position': instance.position,
      'manager': instance.manager,
      'territory': instance.territory,
      'region': instance.region,
      'hireDate': instance.hireDate?.toIso8601String(),
      'employeeId': instance.employeeId,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'bio': instance.bio,
    };

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) => UserSettings(
  theme: json['theme'] as String? ?? 'system',
  language: json['language'] as String? ?? 'en',
  notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
  emailNotifications: json['emailNotifications'] as bool? ?? true,
  pushNotifications: json['pushNotifications'] as bool? ?? true,
  soundEnabled: json['soundEnabled'] as bool? ?? true,
  vibrationEnabled: json['vibrationEnabled'] as bool? ?? true,
  currency: json['currency'] as String? ?? 'USD',
  dateFormat: json['dateFormat'] as String? ?? 'dd/MM/yyyy',
  timeFormat: json['timeFormat'] as String? ?? '24h',
  autoSync: json['autoSync'] as bool? ?? true,
  syncInterval: (json['syncInterval'] as num?)?.toInt() ?? 15,
  biometricAuth: json['biometricAuth'] as bool? ?? false,
  rememberLogin: json['rememberLogin'] as bool? ?? false,
);

Map<String, dynamic> _$UserSettingsToJson(UserSettings instance) =>
    <String, dynamic>{
      'theme': instance.theme,
      'language': instance.language,
      'notificationsEnabled': instance.notificationsEnabled,
      'emailNotifications': instance.emailNotifications,
      'pushNotifications': instance.pushNotifications,
      'soundEnabled': instance.soundEnabled,
      'vibrationEnabled': instance.vibrationEnabled,
      'currency': instance.currency,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
      'autoSync': instance.autoSync,
      'syncInterval': instance.syncInterval,
      'biometricAuth': instance.biometricAuth,
      'rememberLogin': instance.rememberLogin,
    };
