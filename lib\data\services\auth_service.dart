import 'dart:convert';
import 'package:get/get.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user.dart';
import '../../core/config/app_config.dart';
import '../../core/constants/api_endpoints.dart';
import '../../core/constants/app_constants.dart';
import '../../core/exceptions/app_exception.dart';

/// Authentication service for Al Ameen Sales App
class AuthService extends GetConnect {
  static const _storage = FlutterSecureStorage();

  @override
  void onInit() {
    super.onInit();
    
    // Configure base URL
    httpClient.baseUrl = AppConfig.baseUrl;
    
    // Configure timeouts
    httpClient.timeout = Duration(milliseconds: AppConfig.connectTimeout);
    
    // Add request interceptor for authentication
    httpClient.addRequestModifier<dynamic>((request) async {
      final token = await getToken();
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      return request;
    });

    // Add response interceptor for error handling
    httpClient.addResponseModifier((request, response) async {
      if (response.statusCode == 401) {
        // Token expired, try to refresh
        final refreshed = await _refreshToken();
        if (!refreshed) {
          // Refresh failed, logout user
          await logout();
          throw const AuthException('Session expired. Please login again.');
        }
      }
      return response;
    });
  }

  /// Login with email and password
  Future<User> login(String email, String password) async {
    try {
      final response = await post(
        ApiEndpoints.login,
        {
          'email': email,
          'password': password,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }

      final data = response.body['data'];
      final user = User.fromJson(data['user']);
      final token = data['token'] as String;
      final refreshToken = data['refresh_token'] as String?;

      // Store tokens and user data
      await _storeAuthData(user, token, refreshToken);

      return user;
    } catch (e) {
      if (e is AppException) rethrow;
      throw AuthException('Login failed: ${e.toString()}');
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Call logout endpoint if token exists
      final token = await getToken();
      if (token != null) {
        await post(ApiEndpoints.logout, {});
      }
    } catch (e) {
      // Ignore logout API errors
    } finally {
      // Always clear local data
      await _clearAuthData();
    }
  }

  /// Refresh authentication token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) return false;

      final response = await post(
        ApiEndpoints.refreshToken,
        {'refresh_token': refreshToken},
      );

      if (response.hasError) return false;

      final data = response.body['data'];
      final newToken = data['token'] as String;
      final newRefreshToken = data['refresh_token'] as String?;

      // Store new tokens
      await _storage.write(key: AppConstants.tokenKey, value: newToken);
      if (newRefreshToken != null) {
        await _storage.write(key: AppConstants.refreshTokenKey, value: newRefreshToken);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get current user
  Future<User?> getCurrentUser() async {
    try {
      final userJson = await _storage.read(key: AppConstants.userKey);
      if (userJson == null) return null;
      
      final userData = jsonDecode(userJson);
      return User.fromJson(userData);
    } catch (e) {
      return null;
    }
  }

  /// Get stored authentication token
  Future<String?> getToken() async {
    return await _storage.read(key: AppConstants.tokenKey);
  }

  /// Get stored refresh token
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: AppConstants.refreshTokenKey);
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null;
  }

  /// Get user role
  Future<String?> getUserRole() async {
    final user = await getCurrentUser();
    return user?.role;
  }

  /// Update user profile
  Future<User> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await put(ApiEndpoints.updateProfile, profileData);

      if (response.hasError) {
        throw _handleError(response);
      }

      final userData = response.body['data'];
      final user = User.fromJson(userData);

      // Update stored user data
      await _storage.write(
        key: AppConstants.userKey,
        value: jsonEncode(user.toJson()),
      );

      return user;
    } catch (e) {
      if (e is AppException) rethrow;
      throw AuthException('Profile update failed: ${e.toString()}');
    }
  }

  /// Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await post(
        ApiEndpoints.changePassword,
        {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw AuthException('Password change failed: ${e.toString()}');
    }
  }

  /// Forgot password
  Future<void> forgotPassword(String email) async {
    try {
      final response = await post(
        ApiEndpoints.forgotPassword,
        {'email': email},
      );

      if (response.hasError) {
        throw _handleError(response);
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw AuthException('Forgot password failed: ${e.toString()}');
    }
  }

  /// Reset password
  Future<void> resetPassword({
    required String token,
    required String email,
    required String password,
  }) async {
    try {
      final response = await post(
        ApiEndpoints.resetPassword,
        {
          'token': token,
          'email': email,
          'password': password,
        },
      );

      if (response.hasError) {
        throw _handleError(response);
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw AuthException('Password reset failed: ${e.toString()}');
    }
  }

  /// Store authentication data
  Future<void> _storeAuthData(User user, String token, String? refreshToken) async {
    await _storage.write(key: AppConstants.tokenKey, value: token);
    await _storage.write(key: AppConstants.userKey, value: jsonEncode(user.toJson()));
    await _storage.write(key: AppConstants.userRoleKey, value: user.role);
    
    if (refreshToken != null) {
      await _storage.write(key: AppConstants.refreshTokenKey, value: refreshToken);
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    await _storage.delete(key: AppConstants.tokenKey);
    await _storage.delete(key: AppConstants.refreshTokenKey);
    await _storage.delete(key: AppConstants.userKey);
    await _storage.delete(key: AppConstants.userRoleKey);
  }

  /// Handle API errors
  AppException _handleError(Response response) {
    final statusCode = response.statusCode ?? 0;
    final body = response.body;
    
    String message = 'An error occurred';
    String? code;
    
    if (body is Map<String, dynamic>) {
      message = body['message'] ?? message;
      code = body['code'];
    }
    
    return ExceptionFactory.createFromHttpStatus(
      statusCode: statusCode,
      message: message,
      code: code,
      details: body,
    );
  }
}
