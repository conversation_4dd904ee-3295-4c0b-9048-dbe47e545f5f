import '../constants/api_endpoints.dart';

/// Application configuration for Al Ameen Sales App
class AppConfig {
  // Prevent instantiation
  AppConfig._();

  // Environment types
  static const String development = 'development';
  static const String staging = 'staging';
  static const String production = 'production';

  // Current environment (change this for different builds)
  static const String currentEnvironment = development;

  /// Get base URL based on current environment
  static String get baseUrl {
    switch (currentEnvironment) {
      case development:
        return ApiEndpoints.devBaseUrl;
      case staging:
        return ApiEndpoints.stagingBaseUrl;
      case production:
        return ApiEndpoints.baseUrl;
      default:
        return ApiEndpoints.devBaseUrl;
    }
  }

  /// Check if app is in development mode
  static bool get isDevelopment => currentEnvironment == development;

  /// Check if app is in staging mode
  static bool get isStaging => currentEnvironment == staging;

  /// Check if app is in production mode
  static bool get isProduction => currentEnvironment == production;

  /// API Configuration
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  /// Retry Configuration
  static const int maxRetries = 3;
  static const int retryDelay = 1000; // 1 second

  /// Cache Configuration
  static const int cacheMaxAge = 300; // 5 minutes
  static const int cacheMaxStale = 86400; // 24 hours

  /// Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  /// File Upload Configuration
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];

  /// Security Configuration
  static const int tokenExpiryBuffer = 300; // 5 minutes before actual expiry
  static const int maxLoginAttempts = 5;
  static const int lockoutDuration = 900; // 15 minutes

  /// UI Configuration
  static const double animationDuration = 300; // milliseconds
  static const double longAnimationDuration = 500; // milliseconds
  static const int splashScreenDuration = 3000; // 3 seconds

  /// Notification Configuration
  static const int notificationDisplayDuration = 3000; // 3 seconds
  static const int errorNotificationDuration = 5000; // 5 seconds

  /// Search Configuration
  static const int searchDebounceDelay = 500; // milliseconds
  static const int minSearchLength = 2;
  static const int maxSearchResults = 50;

  /// Offline Configuration
  static const int offlineRetryInterval = 5000; // 5 seconds
  static const int maxOfflineRetries = 10;

  /// Analytics Configuration
  static final bool enableAnalytics = !isProduction; // Disable in production for privacy
  static const bool enableCrashReporting = true;
  static final bool enablePerformanceMonitoring = !isProduction;

  /// Debug Configuration
  static final bool enableLogging = !isProduction;
  static final bool enableNetworkLogging = isDevelopment;
  static final bool enableDetailedErrors = !isProduction;

  /// Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableOfflineMode = true;
  static const bool enableAutoSync = true;

  /// App Store Configuration
  static const String appStoreId = 'com.alameen.sales_app';
  static const String playStoreId = 'com.alameen.sales_app';
  static const String appStoreUrl = 'https://apps.apple.com/app/id123456789';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.alameen.sales_app';

  /// Support Configuration
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+967 *********';
  static const String privacyPolicyUrl = 'https://alameen.com/privacy';
  static const String termsOfServiceUrl = 'https://alameen.com/terms';
  static const String helpUrl = 'https://help.alameen.com';

  /// Social Media Configuration
  static const String facebookUrl = 'https://facebook.com/alameen';
  static const String twitterUrl = 'https://twitter.com/alameen';
  static const String linkedinUrl = 'https://linkedin.com/company/alameen';
  static const String instagramUrl = 'https://instagram.com/alameen';

  /// API Keys (These should be loaded from environment variables in production)
  static String get googleMapsApiKey {
    switch (currentEnvironment) {
      case development:
        return 'dev_google_maps_api_key';
      case staging:
        return 'staging_google_maps_api_key';
      case production:
        return 'prod_google_maps_api_key';
      default:
        return 'dev_google_maps_api_key';
    }
  }

  static String get firebaseApiKey {
    switch (currentEnvironment) {
      case development:
        return 'dev_firebase_api_key';
      case staging:
        return 'staging_firebase_api_key';
      case production:
        return 'prod_firebase_api_key';
      default:
        return 'dev_firebase_api_key';
    }
  }

  /// Database Configuration
  static const String databaseName = 'alameen_sales.db';
  static const int databaseVersion = 1;

  /// Sync Configuration
  static const int syncInterval = 300000; // 5 minutes
  static const int backgroundSyncInterval = 900000; // 15 minutes
  static const int forceSyncInterval = 3600000; // 1 hour

  /// Location Configuration
  static const double locationAccuracy = 100.0; // meters
  static const int locationTimeout = 30000; // 30 seconds
  static const int locationUpdateInterval = 60000; // 1 minute

  /// Validation Configuration
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 1000;
  static const int maxCommentLength = 500;

  /// Currency Configuration
  static const String defaultCurrency = 'USD';
  static const String currencySymbol = '\$';
  static const int currencyDecimalPlaces = 2;

  /// Date/Time Configuration
  static const String defaultDateFormat = 'dd/MM/yyyy';
  static const String defaultTimeFormat = 'HH:mm';
  static const String defaultDateTimeFormat = 'dd/MM/yyyy HH:mm';

  /// Theme Configuration
  static const String defaultTheme = 'light';
  static const bool enableSystemTheme = true;

  /// Language Configuration
  static const String defaultLanguage = 'en';
  static const List<String> supportedLanguages = ['en', 'ar'];

  /// Performance Configuration
  static const int imageCompressionQuality = 80;
  static const int maxImageWidth = 1920;
  static const int maxImageHeight = 1080;
  static const int thumbnailSize = 200;

  /// Backup Configuration
  static const int autoBackupInterval = 86400000; // 24 hours
  static const int maxBackupFiles = 7;
  static const bool enableAutoBackup = true;

  /// Get configuration value by key
  static dynamic getConfigValue(String key, {dynamic defaultValue}) {
    final config = _getEnvironmentConfig();
    return config[key] ?? defaultValue;
  }

  /// Get environment-specific configuration
  static Map<String, dynamic> _getEnvironmentConfig() {
    switch (currentEnvironment) {
      case development:
        return _developmentConfig;
      case staging:
        return _stagingConfig;
      case production:
        return _productionConfig;
      default:
        return _developmentConfig;
    }
  }

  /// Development environment configuration
  static const Map<String, dynamic> _developmentConfig = {
    'enableMockData': true,
    'enableDebugMode': true,
    'logLevel': 'debug',
    'apiTimeout': 10000,
  };

  /// Staging environment configuration
  static const Map<String, dynamic> _stagingConfig = {
    'enableMockData': false,
    'enableDebugMode': true,
    'logLevel': 'info',
    'apiTimeout': 20000,
  };

  /// Production environment configuration
  static const Map<String, dynamic> _productionConfig = {
    'enableMockData': false,
    'enableDebugMode': false,
    'logLevel': 'error',
    'apiTimeout': 30000,
  };
}
