import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../data/models/user.dart';

/// Profile card widget for displaying user information
class ProfileCard extends StatelessWidget {
  final User user;
  final Map<String, int>? statistics;
  final VoidCallback? onEdit;
  final VoidCallback? onSettings;

  const ProfileCard({
    super.key,
    required this.user,
    this.statistics,
    this.onEdit,
    this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 4,
        margin: EdgeInsets.all(16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.surface,
              ],
            ),
          ),
          child: Column(
            children: [
              // Header with avatar and basic info
              Row(
                children: [
                  // Avatar
                  _buildAvatar(),
                  
                  SizedBox(width: 16.w),
                  
                  // User info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.name,
                          style: AppTextStyles.titleLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          user.email,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.onSurfaceVariant,
                          ),
                        ),
                        if (user.phone?.isNotEmpty == true) ...[
                          SizedBox(height: 2.h),
                          Text(
                            user.phone!,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Action buttons
                  Column(
                    children: [
                      if (onEdit != null)
                        IconButton(
                          onPressed: onEdit,
                          icon: Icon(
                            Icons.edit,
                            color: AppColors.primary,
                          ),
                          tooltip: 'edit_profile'.tr,
                        ),
                      if (onSettings != null)
                        IconButton(
                          onPressed: onSettings,
                          icon: Icon(
                            Icons.settings,
                            color: AppColors.onSurfaceVariant,
                          ),
                          tooltip: 'settings'.tr,
                        ),
                    ],
                  ),
                ],
              ),
              
              SizedBox(height: 20.h),
              
              // Role and department info
              _buildInfoSection(),
              
              if (statistics != null) ...[
                SizedBox(height: 20.h),
                
                // Statistics
                _buildStatisticsSection(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.7),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: user.avatarUrl?.isNotEmpty == true
          ? ClipOval(
              child: Image.network(
                user.avatarUrl!,
                width: 80.w,
                height: 80.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildAvatarFallback(),
              ),
            )
          : _buildAvatarFallback(),
    );
  }

  Widget _buildAvatarFallback() {
    return Center(
      child: Text(
        user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
        style: AppTextStyles.headlineMedium.copyWith(
          color: AppColors.onPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.work,
            label: 'role'.tr,
            value: _getRoleDisplayName(user.role),
          ),
          if (user.department?.isNotEmpty == true) ...[
            SizedBox(height: 12.h),
            _buildInfoRow(
              icon: Icons.business,
              label: 'department'.tr,
              value: user.department!,
            ),
          ],
          if (user.address?.isNotEmpty == true) ...[
            SizedBox(height: 12.h),
            _buildInfoRow(
              icon: Icons.location_on,
              label: 'address'.tr,
              value: user.address!,
            ),
          ],
          SizedBox(height: 12.h),
          _buildInfoRow(
            icon: Icons.calendar_today,
            label: 'member_since'.tr,
            value: _formatMemberSince(user.createdAt),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18.sp,
          color: AppColors.primary,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'statistics'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatisticItem(
                  label: 'orders'.tr,
                  value: statistics!['total_orders']?.toString() ?? '0',
                  icon: Icons.shopping_cart,
                ),
              ),
              Expanded(
                child: _buildStatisticItem(
                  label: 'customers'.tr,
                  value: statistics!['total_customers']?.toString() ?? '0',
                  icon: Icons.people,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatisticItem(
                  label: 'visits'.tr,
                  value: statistics!['total_visits']?.toString() ?? '0',
                  icon: Icons.event,
                ),
              ),
              Expanded(
                child: _buildStatisticItem(
                  label: 'invoices'.tr,
                  value: statistics!['total_invoices']?.toString() ?? '0',
                  icon: Icons.receipt,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticItem({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 20.sp,
            color: AppColors.primary,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'sales_rep':
        return 'sales_representative'.tr;
      case 'manager':
        return 'manager'.tr;
      case 'admin':
        return 'administrator'.tr;
      default:
        return role;
    }
  }

  String _formatMemberSince(DateTime createdAt) {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays < 30) {
      return '${difference.inDays} ${'days_ago'.tr}';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${'months_ago'.tr}';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${'years_ago'.tr}';
    }
  }
}
