import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../exceptions/app_exception.dart';

/// Storage service for Al Ameen Sales App
class StorageService extends GetxService {
  late SharedPreferences _prefs;
  bool _isInitialized = false;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initialize();
  }

  /// Initialize storage service
  Future<void> _initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
    } catch (e) {
      throw StorageException('Failed to initialize storage: ${e.toString()}');
    }
  }

  /// Ensure storage is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw const StorageException('Storage service not initialized');
    }
  }

  /// Store string value
  Future<bool> setString(String key, String value) async {
    try {
      _ensureInitialized();
      return await _prefs.setString(key, value);
    } catch (e) {
      throw StorageException('Failed to store string: ${e.toString()}');
    }
  }

  /// Get string value
  String? getString(String key) {
    try {
      _ensureInitialized();
      return _prefs.getString(key);
    } catch (e) {
      throw StorageException('Failed to get string: ${e.toString()}');
    }
  }

  /// Store integer value
  Future<bool> setInt(String key, int value) async {
    try {
      _ensureInitialized();
      return await _prefs.setInt(key, value);
    } catch (e) {
      throw StorageException('Failed to store integer: ${e.toString()}');
    }
  }

  /// Get integer value
  int? getInt(String key) {
    try {
      _ensureInitialized();
      return _prefs.getInt(key);
    } catch (e) {
      throw StorageException('Failed to get integer: ${e.toString()}');
    }
  }

  /// Store double value
  Future<bool> setDouble(String key, double value) async {
    try {
      _ensureInitialized();
      return await _prefs.setDouble(key, value);
    } catch (e) {
      throw StorageException('Failed to store double: ${e.toString()}');
    }
  }

  /// Get double value
  double? getDouble(String key) {
    try {
      _ensureInitialized();
      return _prefs.getDouble(key);
    } catch (e) {
      throw StorageException('Failed to get double: ${e.toString()}');
    }
  }

  /// Store boolean value
  Future<bool> setBool(String key, bool value) async {
    try {
      _ensureInitialized();
      return await _prefs.setBool(key, value);
    } catch (e) {
      throw StorageException('Failed to store boolean: ${e.toString()}');
    }
  }

  /// Get boolean value
  bool? getBool(String key) {
    try {
      _ensureInitialized();
      return _prefs.getBool(key);
    } catch (e) {
      throw StorageException('Failed to get boolean: ${e.toString()}');
    }
  }

  /// Store list of strings
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      _ensureInitialized();
      return await _prefs.setStringList(key, value);
    } catch (e) {
      throw StorageException('Failed to store string list: ${e.toString()}');
    }
  }

  /// Get list of strings
  List<String>? getStringList(String key) {
    try {
      _ensureInitialized();
      return _prefs.getStringList(key);
    } catch (e) {
      throw StorageException('Failed to get string list: ${e.toString()}');
    }
  }

  /// Store JSON object
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      _ensureInitialized();
      final jsonString = jsonEncode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      throw StorageException('Failed to store JSON: ${e.toString()}');
    }
  }

  /// Get JSON object
  Map<String, dynamic>? getJson(String key) {
    try {
      _ensureInitialized();
      final jsonString = _prefs.getString(key);
      if (jsonString == null) return null;
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw StorageException('Failed to get JSON: ${e.toString()}');
    }
  }

  /// Remove value by key
  Future<bool> remove(String key) async {
    try {
      _ensureInitialized();
      return await _prefs.remove(key);
    } catch (e) {
      throw StorageException('Failed to remove value: ${e.toString()}');
    }
  }

  /// Check if key exists
  bool containsKey(String key) {
    try {
      _ensureInitialized();
      return _prefs.containsKey(key);
    } catch (e) {
      throw StorageException('Failed to check key: ${e.toString()}');
    }
  }

  /// Get all keys
  Set<String> getKeys() {
    try {
      _ensureInitialized();
      return _prefs.getKeys();
    } catch (e) {
      throw StorageException('Failed to get keys: ${e.toString()}');
    }
  }

  /// Clear all data
  Future<bool> clear() async {
    try {
      _ensureInitialized();
      return await _prefs.clear();
    } catch (e) {
      throw StorageException('Failed to clear storage: ${e.toString()}');
    }
  }

  /// Get storage size (approximate)
  int getStorageSize() {
    try {
      _ensureInitialized();
      int size = 0;
      for (String key in _prefs.getKeys()) {
        final value = _prefs.get(key);
        if (value is String) {
          size += value.length;
        } else if (value is List<String>) {
          size += value.join('').length;
        }
      }
      return size;
    } catch (e) {
      return 0;
    }
  }

  /// Export all data
  Map<String, dynamic> exportData() {
    try {
      _ensureInitialized();
      final Map<String, dynamic> data = {};
      for (String key in _prefs.getKeys()) {
        data[key] = _prefs.get(key);
      }
      return data;
    } catch (e) {
      throw StorageException('Failed to export data: ${e.toString()}');
    }
  }

  /// Import data
  Future<void> importData(Map<String, dynamic> data) async {
    try {
      _ensureInitialized();
      for (String key in data.keys) {
        final value = data[key];
        if (value is String) {
          await _prefs.setString(key, value);
        } else if (value is int) {
          await _prefs.setInt(key, value);
        } else if (value is double) {
          await _prefs.setDouble(key, value);
        } else if (value is bool) {
          await _prefs.setBool(key, value);
        } else if (value is List<String>) {
          await _prefs.setStringList(key, value);
        }
      }
    } catch (e) {
      throw StorageException('Failed to import data: ${e.toString()}');
    }
  }
}

/// Storage exception class
class StorageException extends AppException {
  const StorageException(super.message, {super.code, super.details});

  @override
  String toString() => 'StorageException: $message';
}

/// Storage keys constants
class StorageKeys {
  static const String userToken = 'user_token';
  static const String userData = 'user_data';
  static const String userSettings = 'user_settings';
  static const String appTheme = 'app_theme';
  static const String appLanguage = 'app_language';
  static const String isFirstLaunch = 'is_first_launch';
  static const String lastSyncTime = 'last_sync_time';
  static const String offlineData = 'offline_data';
  static const String cacheData = 'cache_data';
  static const String notificationSettings = 'notification_settings';
  static const String biometricEnabled = 'biometric_enabled';
  static const String rememberLogin = 'remember_login';
  static const String autoSync = 'auto_sync';
  static const String syncInterval = 'sync_interval';
}
