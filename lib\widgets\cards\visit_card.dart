import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/visit.dart';

/// Visit card widget for displaying visit information
class VisitCard extends StatelessWidget {
  final Visit visit;
  final VoidCallback? onTap;
  final VoidCallback? onStart;
  final VoidCallback? onComplete;
  final VoidCallback? onCancel;
  final VoidCallback? onEdit;

  const VisitCard({
    super.key,
    required this.visit,
    this.onTap,
    this.onStart,
    this.onComplete,
    this.onCancel,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: visit.isOverdue 
                ? Border.all(color: AppColors.error, width: 1)
                : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            visit.customerName,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            visit.formattedVisitNumber,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Status chip
                    _buildStatusChip(),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    _buildActionsMenu(),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Purpose
                _buildDetailItem(
                  icon: Icons.assignment,
                  label: 'purpose'.tr,
                  value: _getPurposeDisplayName(visit.purpose),
                ),
                
                SizedBox(height: 8.h),
                
                // Visit details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.schedule,
                        label: 'scheduled_time'.tr,
                        value: FormatUtils.formatDateTime(visit.scheduledDate),
                      ),
                    ),
                    if (visit.duration != null) ...[
                      SizedBox(width: 16.w),
                      Expanded(
                        child: _buildDetailItem(
                          icon: Icons.timer,
                          label: 'duration'.tr,
                          value: visit.formattedDuration,
                        ),
                      ),
                    ],
                  ],
                ),
                
                if (visit.location?.address != null) ...[
                  SizedBox(height: 8.h),
                  _buildDetailItem(
                    icon: Icons.location_on,
                    label: 'location'.tr,
                    value: visit.location!.address!,
                  ),
                ],
                
                if (visit.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 8.h),
                  _buildDetailItem(
                    icon: Icons.note,
                    label: 'notes'.tr,
                    value: visit.notes!,
                  ),
                ],
                
                // Action buttons for active visits
                if (_shouldShowActionButtons()) ...[
                  SizedBox(height: 12.h),
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (visit.status) {
      case 'scheduled':
        statusColor = visit.isOverdue ? AppColors.error : AppColors.info;
        statusText = visit.isOverdue ? 'overdue'.tr : 'scheduled'.tr;
        statusIcon = visit.isOverdue ? Icons.warning : Icons.schedule;
        break;
      case 'in_progress':
        statusColor = AppColors.warning;
        statusText = 'in_progress'.tr;
        statusIcon = Icons.play_circle;
        break;
      case 'completed':
        statusColor = AppColors.success;
        statusText = 'completed'.tr;
        statusIcon = Icons.check_circle;
        break;
      case 'cancelled':
        statusColor = AppColors.error;
        statusText = 'cancelled'.tr;
        statusIcon = Icons.cancel;
        break;
      case 'missed':
        statusColor = AppColors.error;
        statusText = 'missed'.tr;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = AppColors.grey500;
        statusText = visit.status;
        statusIcon = Icons.help;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 14.sp,
            color: statusColor,
          ),
          SizedBox(width: 4.w),
          Text(
            statusText,
            style: AppTextStyles.labelSmall.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsMenu() {
    final actions = <PopupMenuItem<String>>[];

    if (visit.canBeStarted && onStart != null) {
      actions.add(
        PopupMenuItem(
          value: 'start',
          child: Row(
            children: [
              Icon(Icons.play_arrow, size: 18.sp, color: AppColors.success),
              SizedBox(width: 8.w),
              Text('start_visit'.tr),
            ],
          ),
        ),
      );
    }

    if (visit.canBeCompleted && onComplete != null) {
      actions.add(
        PopupMenuItem(
          value: 'complete',
          child: Row(
            children: [
              Icon(Icons.check, size: 18.sp, color: AppColors.success),
              SizedBox(width: 8.w),
              Text('complete_visit'.tr),
            ],
          ),
        ),
      );
    }

    if (onEdit != null) {
      actions.add(
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18.sp),
              SizedBox(width: 8.w),
              Text('edit'.tr),
            ],
          ),
        ),
      );
    }

    if (visit.canBeCancelled && onCancel != null) {
      actions.add(
        PopupMenuItem(
          value: 'cancel',
          child: Row(
            children: [
              Icon(Icons.cancel, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text('cancel'.tr),
            ],
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'start':
            onStart?.call();
            break;
          case 'complete':
            onComplete?.call();
            break;
          case 'edit':
            onEdit?.call();
            break;
          case 'cancel':
            onCancel?.call();
            break;
        }
      },
      itemBuilder: (context) => actions,
      child: Icon(
        Icons.more_vert,
        color: AppColors.onSurfaceVariant,
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: AppColors.onSurfaceVariant,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _shouldShowActionButtons() {
    return visit.canBeStarted || visit.canBeCompleted;
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (visit.canBeStarted && onStart != null) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onStart,
              icon: Icon(Icons.play_arrow, size: 18.sp),
              label: Text('start_visit'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 8.h),
              ),
            ),
          ),
        ],
        if (visit.canBeCompleted && onComplete != null) ...[
          if (visit.canBeStarted) SizedBox(width: 8.w),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onComplete,
              icon: Icon(Icons.check, size: 18.sp),
              label: Text('complete_visit'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 8.h),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _getPurposeDisplayName(String purpose) {
    switch (purpose) {
      case 'sales_call':
        return 'sales_call'.tr;
      case 'delivery':
        return 'delivery'.tr;
      case 'collection':
        return 'collection'.tr;
      case 'support':
        return 'support'.tr;
      case 'follow_up':
        return 'follow_up'.tr;
      case 'maintenance':
        return 'maintenance'.tr;
      case 'training':
        return 'training'.tr;
      case 'survey':
        return 'survey'.tr;
      default:
        return purpose;
    }
  }
}
