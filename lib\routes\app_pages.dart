import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_routes.dart';
import '../features/common/splash/splash_screen.dart';
import '../features/common/splash/splash_binding.dart';
import '../features/common/auth/login_screen.dart';
import '../features/common/auth/login_binding.dart';
import '../features/retail/dashboard/retail_dashboard_screen.dart';
import '../features/retail/dashboard/retail_dashboard_binding.dart';
import '../features/retail/orders/orders_screen.dart';
import '../features/retail/orders/orders_binding.dart';
import '../features/retail/orders/create_order_screen.dart';
import '../features/retail/orders/create_order_binding.dart';
import '../features/retail/customers/customers_screen.dart';
import '../features/retail/customers/customers_binding.dart';
import '../features/retail/visits/visits_screen.dart';
import '../features/retail/visits/visits_binding.dart';
import '../features/retail/invoices/invoices_screen.dart';
import '../features/retail/invoices/invoices_binding.dart';
import '../features/retail/returns/returns_screen.dart';
import '../features/retail/returns/returns_binding.dart';
import '../features/retail/notifications/notifications_screen.dart';
import '../features/retail/notifications/notifications_binding.dart';
import '../features/retail/profile/profile_screen.dart';
import '../features/retail/profile/profile_binding.dart';
import '../features/retail/settings/settings_screen.dart';
import '../features/retail/settings/settings_binding.dart';
import '../bindings/initial_binding.dart';

// Import placeholder screens (will be created later)
// import '../features/wholesale/dashboard/wholesale_dashboard_screen.dart';

/// Application pages configuration for GetX routing
class AppPages {
  // Prevent instantiation
  AppPages._();

  /// Initial route
  static const String initial = AppRoutes.splash;

  /// All application routes
  static final routes = [
    // Splash screen
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),

    // Login screen
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
      binding: LoginBinding(),
    ),

    // Retail routes
    GetPage(
      name: AppRoutes.retailDashboard,
      page: () => const RetailDashboardScreen(),
      binding: RetailDashboardBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailOrders,
      page: () => const OrdersScreen(),
      binding: OrdersBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailOrderDetails,
      page: () => const Placeholder(), // RetailOrderDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCreateOrder,
      page: () => const CreateOrderScreen(),
      binding: CreateOrderBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCustomers,
      page: () => const CustomersScreen(),
      binding: CustomersBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCustomerDetails,
      page: () => const Placeholder(), // RetailCustomerDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCreateCustomer,
      page: () => const Placeholder(), // RetailCreateCustomerScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailVisits,
      page: () => const VisitsScreen(),
      binding: VisitsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailVisitDetails,
      page: () => const Placeholder(), // RetailVisitDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailScheduleVisit,
      page: () => const Placeholder(), // RetailScheduleVisitScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailInvoices,
      page: () => const InvoicesScreen(),
      binding: InvoicesBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailInvoiceDetails,
      page: () => const Placeholder(), // RetailInvoiceDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCreateInvoice,
      page: () => const Placeholder(), // RetailCreateInvoiceScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailReturns,
      page: () => const ReturnsScreen(),
      binding: ReturnsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailReturnDetails,
      page: () => const Placeholder(), // RetailReturnDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailCreateReturn,
      page: () => const Placeholder(), // RetailCreateReturnScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailNotifications,
      page: () => const NotificationsScreen(),
      binding: NotificationsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailProfile,
      page: () => const ProfileScreen(),
      binding: ProfileBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.retailSettings,
      page: () => const SettingsScreen(),
      binding: SettingsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    // Wholesale routes
    GetPage(
      name: AppRoutes.wholesaleDashboard,
      page: () => const Placeholder(), // WholesaleDashboardScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleOrders,
      page: () => const Placeholder(), // WholesaleOrderListScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleOrderDetails,
      page: () => const Placeholder(), // WholesaleOrderDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleCreateOrder,
      page: () => const Placeholder(), // WholesaleCreateOrderScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleCustomers,
      page: () => const Placeholder(), // WholesaleCustomerListScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleCustomerDetails,
      page: () => const Placeholder(), // WholesaleCustomerDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleCreateCustomer,
      page: () => const Placeholder(), // WholesaleCreateCustomerScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleVisits,
      page: () => const Placeholder(), // WholesaleVisitListScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleVisitDetails,
      page: () => const Placeholder(), // WholesaleVisitDetailScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleScheduleVisit,
      page: () => const Placeholder(), // WholesaleScheduleVisitScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.wholesaleNotifications,
      page: () => const Placeholder(), // WholesaleNotificationListScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    // Profile and settings
    GetPage(
      name: AppRoutes.profile,
      page: () => const Placeholder(), // ProfileScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.settings,
      page: () => const Placeholder(), // SettingsScreen(),
      binding: InitialBinding(),
      middlewares: [AuthMiddleware()],
    ),
  ];
}

/// Authentication middleware to protect routes
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // This will be implemented when AuthService is available
    // For now, allow all routes
    return null;
  }
}
