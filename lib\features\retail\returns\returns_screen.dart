import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'returns_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../core/utils/format_utils.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/cards/return_card.dart';

/// Returns list screen for retail representatives
class ReturnsScreen extends GetView<ReturnsController> {
  const ReturnsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            // Summary cards
            _buildSummaryCards(),
            
            // Search and filters
            _buildSearchAndFilters(),
            
            // Returns list
            Expanded(
              child: _buildReturnsList(),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('returns'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: controller.clearFilters,
          icon: const Icon(Icons.clear_all),
          tooltip: 'clear_filters'.tr,
        ),
        IconButton(
          onPressed: controller.refreshReturns,
          icon: const Icon(Icons.refresh),
          tooltip: 'refresh'.tr,
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() => Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              title: 'total_returns'.tr,
              value: FormatUtils.formatCurrency(controller.totalReturnAmount),
              icon: Icons.assignment_return,
              color: AppColors.primary,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildSummaryCard(
              title: 'approved_returns'.tr,
              value: FormatUtils.formatCurrency(controller.approvedReturnAmount),
              icon: Icons.check_circle,
              color: AppColors.success,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildSummaryCard(
              title: 'pending_returns'.tr,
              value: FormatUtils.formatCurrency(controller.pendingReturnAmount),
              icon: Icons.pending,
              color: AppColors.warning,
            ),
          ),
        ],
      )),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field
          CustomTextField(
            hintText: 'search_returns'.tr,
            prefixIcon: Icons.search,
            onChanged: controller.updateSearchQuery,
            isRTL: true,
          ),
          
          SizedBox(height: 12.h),
          
          // Filter chips
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  label: 'status'.tr,
                  value: controller.selectedStatus,
                  options: controller.statusOptions,
                  onChanged: controller.updateStatusFilter,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildFilterChip(
                  label: 'reason'.tr,
                  value: controller.selectedReason,
                  options: controller.reasonOptions,
                  onChanged: controller.updateReasonFilter,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildFilterChip(
                  label: 'date_range'.tr,
                  value: controller.selectedDateRange,
                  options: controller.dateRangeOptions,
                  onChanged: controller.updateDateRangeFilter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required RxString value,
    required List<String> options,
    required Function(String) onChanged,
  }) {
    return Obx(() => Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value.value,
          isExpanded: true,
          hint: Text(label, style: AppTextStyles.labelSmall),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(
                option == 'all' ? 'all'.tr : option.tr,
                style: AppTextStyles.labelSmall,
              ),
            );
          }).toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ),
    ));
  }

  Widget _buildReturnsList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (controller.filteredReturns.isEmpty) {
        return _buildEmptyState();
      }

      return RefreshIndicator(
        onRefresh: controller.refreshReturns,
        child: ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: controller.filteredReturns.length,
          itemBuilder: (context, index) {
            final returnItem = controller.filteredReturns[index];
            return ReturnCard(
              returnItem: returnItem,
              onTap: () => controller.navigateToReturnDetails(returnItem.id),
              onApprove: () => _showApproveReturnDialog(returnItem.id),
              onReject: () => _showRejectReturnDialog(returnItem.id),
              onProcess: () => controller.processReturn(returnItem.id),
              onCancel: () => _showCancelReturnDialog(returnItem.id),
              onEdit: () => controller.navigateToEditReturn(returnItem.id),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_return,
            size: 64.sp,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            'no_returns_found'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'try_adjusting_filters'.tr,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: controller.navigateToCreateReturn,
            icon: const Icon(Icons.add),
            label: Text('create_return'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: controller.navigateToCreateReturn,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      child: const Icon(Icons.add),
    );
  }

  void _showApproveReturnDialog(String returnId) {
    final notesController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('approve_return'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('add_approval_notes'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: notesController,
                hintText: 'enter_approval_notes'.tr,
                maxLines: 3,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('cancel'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                controller.approveReturn(returnId, notesController.text.trim());
              },
              child: Text('approve'.tr),
            ),
          ],
        ),
      ),
    );
  }

  void _showRejectReturnDialog(String returnId) {
    final reasonController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('reject_return'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('rejection_reason_required'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: reasonController,
                hintText: 'enter_rejection_reason'.tr,
                maxLines: 3,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('cancel'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                if (reasonController.text.trim().isNotEmpty) {
                  Get.back();
                  controller.rejectReturn(returnId, reasonController.text.trim());
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: Text('reject'.tr),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelReturnDialog(String returnId) {
    final reasonController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('cancel_return'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('cancel_return_reason'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: reasonController,
                hintText: 'enter_cancel_reason'.tr,
                maxLines: 2,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('no'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                controller.cancelReturn(returnId, reasonController.text.trim());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: Text('yes'.tr),
            ),
          ],
        ),
      ),
    );
  }
}
