// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Invoice _$InvoiceFromJson(Map<String, dynamic> json) => Invoice(
  id: json['id'] as String,
  invoiceNumber: json['invoiceNumber'] as String,
  orderId: json['orderId'] as String?,
  customerId: json['customerId'] as String,
  customerName: json['customerName'] as String,
  status: json['status'] as String,
  invoiceDate: DateTime.parse(json['invoiceDate'] as String),
  dueDate: DateTime.parse(json['dueDate'] as String),
  items:
      (json['items'] as List<dynamic>)
          .map((e) => InvoiceItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  subtotal: (json['subtotal'] as num).toDouble(),
  taxAmount: (json['taxAmount'] as num).toDouble(),
  discountAmount: (json['discountAmount'] as num).toDouble(),
  totalAmount: (json['totalAmount'] as num).toDouble(),
  paidAmount: (json['paidAmount'] as num?)?.toDouble() ?? 0.0,
  balanceAmount: (json['balanceAmount'] as num).toDouble(),
  paymentTerms: json['paymentTerms'] as String,
  notes: json['notes'] as String?,
  customerAddress: json['customerAddress'] as String?,
  settings:
      json['settings'] == null
          ? null
          : InvoiceSettings.fromJson(json['settings'] as Map<String, dynamic>),
  createdBy: json['createdBy'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  sentAt:
      json['sentAt'] == null ? null : DateTime.parse(json['sentAt'] as String),
  paidAt:
      json['paidAt'] == null ? null : DateTime.parse(json['paidAt'] as String),
);

Map<String, dynamic> _$InvoiceToJson(Invoice instance) => <String, dynamic>{
  'id': instance.id,
  'invoiceNumber': instance.invoiceNumber,
  'orderId': instance.orderId,
  'customerId': instance.customerId,
  'customerName': instance.customerName,
  'status': instance.status,
  'invoiceDate': instance.invoiceDate.toIso8601String(),
  'dueDate': instance.dueDate.toIso8601String(),
  'items': instance.items,
  'subtotal': instance.subtotal,
  'taxAmount': instance.taxAmount,
  'discountAmount': instance.discountAmount,
  'totalAmount': instance.totalAmount,
  'paidAmount': instance.paidAmount,
  'balanceAmount': instance.balanceAmount,
  'paymentTerms': instance.paymentTerms,
  'notes': instance.notes,
  'customerAddress': instance.customerAddress,
  'settings': instance.settings,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'sentAt': instance.sentAt?.toIso8601String(),
  'paidAt': instance.paidAt?.toIso8601String(),
};

InvoiceItem _$InvoiceItemFromJson(Map<String, dynamic> json) => InvoiceItem(
  id: json['id'] as String,
  productId: json['productId'] as String,
  productName: json['productName'] as String,
  productCode: json['productCode'] as String?,
  description: json['description'] as String?,
  quantity: (json['quantity'] as num).toInt(),
  unitPrice: (json['unitPrice'] as num).toDouble(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  discountAmount: (json['discountAmount'] as num?)?.toDouble(),
  taxAmount: (json['taxAmount'] as num?)?.toDouble(),
  unit: json['unit'] as String? ?? 'pcs',
);

Map<String, dynamic> _$InvoiceItemToJson(InvoiceItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'productName': instance.productName,
      'productCode': instance.productCode,
      'description': instance.description,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'totalPrice': instance.totalPrice,
      'discountAmount': instance.discountAmount,
      'taxAmount': instance.taxAmount,
      'unit': instance.unit,
    };

InvoiceSettings _$InvoiceSettingsFromJson(Map<String, dynamic> json) =>
    InvoiceSettings(
      logoUrl: json['logoUrl'] as String?,
      companyName: json['companyName'] as String?,
      companyAddress: json['companyAddress'] as String?,
      companyPhone: json['companyPhone'] as String?,
      companyEmail: json['companyEmail'] as String?,
      companyWebsite: json['companyWebsite'] as String?,
      taxNumber: json['taxNumber'] as String?,
      bankDetails: json['bankDetails'] as String?,
      footerText: json['footerText'] as String?,
      currency: json['currency'] as String? ?? 'USD',
      template: json['template'] as String? ?? 'default',
    );

Map<String, dynamic> _$InvoiceSettingsToJson(InvoiceSettings instance) =>
    <String, dynamic>{
      'logoUrl': instance.logoUrl,
      'companyName': instance.companyName,
      'companyAddress': instance.companyAddress,
      'companyPhone': instance.companyPhone,
      'companyEmail': instance.companyEmail,
      'companyWebsite': instance.companyWebsite,
      'taxNumber': instance.taxNumber,
      'bankDetails': instance.bankDetails,
      'footerText': instance.footerText,
      'currency': instance.currency,
      'template': instance.template,
    };
