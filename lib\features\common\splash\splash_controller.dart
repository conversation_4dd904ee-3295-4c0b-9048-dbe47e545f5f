import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../routes/app_routes.dart';
import '../../../core/constants/app_constants.dart';

/// Splash screen controller for Al Ameen Sales App
class SplashController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  /// Initialize the application
  Future<void> _initializeApp() async {
    try {
      // Wait for splash duration
      await Future.delayed(AppConstants.splashDuration);

      // Check authentication status
      final isAuthenticated = await _authService.isAuthenticated();
      
      if (isAuthenticated) {
        // Get user role and navigate to appropriate dashboard
        final userRole = await _authService.getUserRole();
        
        if (userRole == AppConstants.retailRole) {
          Get.offAllNamed(AppRoutes.retailDashboard);
        } else if (userRole == AppConstants.wholesaleRole) {
          Get.offAllNamed(AppRoutes.wholesaleDashboard);
        } else {
          // Invalid role, logout and go to login
          await _authService.logout();
          Get.offAllNamed(AppRoutes.login);
        }
      } else {
        // Not authenticated, go to login
        Get.offAllNamed(AppRoutes.login);
      }
    } catch (e) {
      // Error during initialization, go to login
      Get.offAllNamed(AppRoutes.login);
    }
  }
}
