// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'return.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Return _$ReturnFromJson(Map<String, dynamic> json) => Return(
  id: json['id'] as String,
  returnNumber: json['returnNumber'] as String,
  orderId: json['orderId'] as String,
  invoiceId: json['invoiceId'] as String?,
  customerId: json['customerId'] as String,
  customerName: json['customerName'] as String,
  status: json['status'] as String,
  reason: json['reason'] as String,
  description: json['description'] as String?,
  requestDate: DateTime.parse(json['requestDate'] as String),
  approvedDate:
      json['approvedDate'] == null
          ? null
          : DateTime.parse(json['approvedDate'] as String),
  processedDate:
      json['processedDate'] == null
          ? null
          : DateTime.parse(json['processedDate'] as String),
  completedDate:
      json['completedDate'] == null
          ? null
          : DateTime.parse(json['completedDate'] as String),
  items:
      (json['items'] as List<dynamic>)
          .map((e) => ReturnItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  totalAmount: (json['totalAmount'] as num).toDouble(),
  refundAmount: (json['refundAmount'] as num).toDouble(),
  refundMethod: json['refundMethod'] as String,
  notes: json['notes'] as String?,
  attachments:
      (json['attachments'] as List<dynamic>?)?.map((e) => e as String).toList(),
  requestedBy: json['requestedBy'] as String,
  approvedBy: json['approvedBy'] as String?,
  processedBy: json['processedBy'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  rejectionReason: json['rejectionReason'] as String?,
);

Map<String, dynamic> _$ReturnToJson(Return instance) => <String, dynamic>{
  'id': instance.id,
  'returnNumber': instance.returnNumber,
  'orderId': instance.orderId,
  'invoiceId': instance.invoiceId,
  'customerId': instance.customerId,
  'customerName': instance.customerName,
  'status': instance.status,
  'reason': instance.reason,
  'description': instance.description,
  'requestDate': instance.requestDate.toIso8601String(),
  'approvedDate': instance.approvedDate?.toIso8601String(),
  'processedDate': instance.processedDate?.toIso8601String(),
  'completedDate': instance.completedDate?.toIso8601String(),
  'items': instance.items,
  'totalAmount': instance.totalAmount,
  'refundAmount': instance.refundAmount,
  'refundMethod': instance.refundMethod,
  'notes': instance.notes,
  'attachments': instance.attachments,
  'requestedBy': instance.requestedBy,
  'approvedBy': instance.approvedBy,
  'processedBy': instance.processedBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'rejectionReason': instance.rejectionReason,
};

ReturnItem _$ReturnItemFromJson(Map<String, dynamic> json) => ReturnItem(
  id: json['id'] as String,
  productId: json['productId'] as String,
  productName: json['productName'] as String,
  productCode: json['productCode'] as String?,
  productImage: json['productImage'] as String?,
  quantity: (json['quantity'] as num).toInt(),
  returnedQuantity: (json['returnedQuantity'] as num).toInt(),
  unitPrice: (json['unitPrice'] as num).toDouble(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  refundAmount: (json['refundAmount'] as num).toDouble(),
  condition: json['condition'] as String,
  notes: json['notes'] as String?,
  unit: json['unit'] as String? ?? 'pcs',
);

Map<String, dynamic> _$ReturnItemToJson(ReturnItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'productName': instance.productName,
      'productCode': instance.productCode,
      'productImage': instance.productImage,
      'quantity': instance.quantity,
      'returnedQuantity': instance.returnedQuantity,
      'unitPrice': instance.unitPrice,
      'totalPrice': instance.totalPrice,
      'refundAmount': instance.refundAmount,
      'condition': instance.condition,
      'notes': instance.notes,
      'unit': instance.unit,
    };
