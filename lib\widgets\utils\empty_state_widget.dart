import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../forms/custom_button.dart';

/// Empty state widget for displaying when no data is available
class EmptyStateWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final IconData? icon;
  final Widget? illustration;
  final String? actionText;
  final VoidCallback? onAction;
  final bool showAction;
  final EdgeInsetsGeometry? padding;
  final EmptyStateType type;

  const EmptyStateWidget({
    super.key,
    this.title,
    this.message,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.showAction = true,
    this.padding,
    this.type = EmptyStateType.general,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIllustration(),
          SizedBox(height: 24.h),
          _buildTitle(),
          SizedBox(height: 12.h),
          _buildMessage(),
          if (showAction && actionText != null && onAction != null) ...[
            SizedBox(height: 32.h),
            _buildAction(),
          ],
        ],
      ),
    );
  }

  Widget _buildIllustration() {
    if (illustration != null) {
      return illustration!;
    }

    return Container(
      width: 120.w,
      height: 120.w,
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(60.r),
      ),
      child: Icon(
        icon ?? _getDefaultIcon(),
        size: 60.w,
        color: AppColors.onSurfaceVariant,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      (title ?? _getDefaultTitle()).tr,
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.onSurface,
        fontWeight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildMessage() {
    return Text(
      (message ?? _getDefaultMessage()).tr,
      style: AppTextStyles.bodyLarge.copyWith(
        color: AppColors.onSurfaceVariant,
      ),
      textAlign: TextAlign.center,
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildAction() {
    return CustomButton(
      text: actionText!,
      onPressed: onAction,
      type: ButtonType.primary,
      size: ButtonSize.medium,
    );
  }

  IconData _getDefaultIcon() {
    switch (type) {
      case EmptyStateType.general:
        return Icons.inbox_outlined;
      case EmptyStateType.search:
        return Icons.search_off;
      case EmptyStateType.orders:
        return Icons.shopping_cart_outlined;
      case EmptyStateType.customers:
        return Icons.people_outline;
      case EmptyStateType.visits:
        return Icons.location_on_outlined;
      case EmptyStateType.invoices:
        return Icons.receipt_outlined;
      case EmptyStateType.returns:
        return Icons.keyboard_return_outlined;
      case EmptyStateType.notifications:
        return Icons.notifications_none;
      case EmptyStateType.network:
        return Icons.wifi_off;
    }
  }

  String _getDefaultTitle() {
    switch (type) {
      case EmptyStateType.general:
        return 'no_data_available';
      case EmptyStateType.search:
        return 'no_search_results';
      case EmptyStateType.orders:
        return 'no_orders_found';
      case EmptyStateType.customers:
        return 'no_customers_found';
      case EmptyStateType.visits:
        return 'no_visits_found';
      case EmptyStateType.invoices:
        return 'no_invoices_found';
      case EmptyStateType.returns:
        return 'no_returns_found';
      case EmptyStateType.notifications:
        return 'no_notifications';
      case EmptyStateType.network:
        return 'no_internet_connection';
    }
  }

  String _getDefaultMessage() {
    switch (type) {
      case EmptyStateType.general:
        return 'no_data_message';
      case EmptyStateType.search:
        return 'try_different_search_terms';
      case EmptyStateType.orders:
        return 'no_orders_message';
      case EmptyStateType.customers:
        return 'no_customers_message';
      case EmptyStateType.visits:
        return 'no_visits_message';
      case EmptyStateType.invoices:
        return 'no_invoices_message';
      case EmptyStateType.returns:
        return 'no_returns_message';
      case EmptyStateType.notifications:
        return 'no_notifications_message';
      case EmptyStateType.network:
        return 'check_internet_connection';
    }
  }
}

/// Empty state types for different contexts
enum EmptyStateType {
  general,
  search,
  orders,
  customers,
  visits,
  invoices,
  returns,
  notifications,
  network,
}

/// Predefined empty state widgets for common scenarios
class AppEmptyStates {
  static EmptyStateWidget get noOrders => EmptyStateWidget(
    type: EmptyStateType.orders,
    actionText: 'add_order',
    onAction: () => Get.toNamed('/orders/create'),
  );

  static EmptyStateWidget get noCustomers => EmptyStateWidget(
    type: EmptyStateType.customers,
    actionText: 'add_customer',
    onAction: () => Get.toNamed('/customers/create'),
  );

  static EmptyStateWidget get noVisits => EmptyStateWidget(
    type: EmptyStateType.visits,
    actionText: 'schedule_visit',
    onAction: () => Get.toNamed('/visits/create'),
  );

  static EmptyStateWidget get noInvoices => EmptyStateWidget(
    type: EmptyStateType.invoices,
    actionText: 'create_invoice',
    onAction: () => Get.toNamed('/invoices/create'),
  );

  static EmptyStateWidget get noReturns => EmptyStateWidget(
    type: EmptyStateType.returns,
    showAction: false,
  );

  static EmptyStateWidget get noNotifications => EmptyStateWidget(
    type: EmptyStateType.notifications,
    showAction: false,
  );

  static EmptyStateWidget get noSearchResults => EmptyStateWidget(
    type: EmptyStateType.search,
    showAction: false,
  );

  static EmptyStateWidget get noInternetConnection => EmptyStateWidget(
    type: EmptyStateType.network,
    actionText: 'retry',
    onAction: () {
      // Implement retry logic
    },
  );
}

/// Empty state with custom illustration
class IllustratedEmptyState extends StatelessWidget {
  final String illustrationPath;
  final String title;
  final String message;
  final String? actionText;
  final VoidCallback? onAction;

  const IllustratedEmptyState({
    super.key,
    required this.illustrationPath,
    required this.title,
    required this.message,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: title,
      message: message,
      actionText: actionText,
      onAction: onAction,
      illustration: Image.asset(
        illustrationPath,
        width: 200.w,
        height: 200.w,
        fit: BoxFit.contain,
      ),
    );
  }
}

/// Compact empty state for smaller spaces
class CompactEmptyState extends StatelessWidget {
  final String message;
  final IconData? icon;
  final VoidCallback? onRetry;

  const CompactEmptyState({
    super.key,
    required this.message,
    this.icon,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? Icons.inbox_outlined,
            size: 48.w,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(height: 12.h),
          Text(
            message.tr,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            SizedBox(height: 16.h),
            TextButton(
              onPressed: onRetry,
              child: Text('retry'.tr),
            ),
          ],
        ],
      ),
    );
  }
}
