{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Flutter Application\\al_ameen_app\\android\\app\\.cxx\\RelWithDebInfo\\6j202317\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Flutter Application\\al_ameen_app\\android\\app\\.cxx\\RelWithDebInfo\\6j202317\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}