import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'profile_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/cards/profile_card.dart';

/// Profile screen for user management
class ProfileScreen extends GetView<ProfileController> {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Obx(() {
          if (controller.isLoading.value && controller.user.value == null) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.user.value == null) {
            return _buildErrorState();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Profile card
                if (!controller.isEditing.value)
                  ProfileCard(
                    user: controller.user.value!,
                    statistics: controller.userStatistics,
                    onEdit: controller.toggleEditMode,
                    onSettings: controller.navigateToSettings,
                  ),

                // Edit form
                if (controller.isEditing.value) _buildEditForm(),

                // Action buttons
                _buildActionButtons(),

                SizedBox(height: 20.h),
              ],
            ),
          );
        }),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('profile'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [
        Obx(() => controller.isEditing.value
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: controller.toggleEditMode,
                    icon: const Icon(Icons.close),
                    tooltip: 'cancel'.tr,
                  ),
                  IconButton(
                    onPressed: controller.isLoading.value ? null : controller.updateProfile,
                    icon: controller.isLoading.value
                        ? SizedBox(
                            width: 20.w,
                            height: 20.w,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.check),
                    tooltip: 'save'.tr,
                  ),
                ],
              )
            : IconButton(
                onPressed: controller.navigateToSettings,
                icon: const Icon(Icons.settings),
                tooltip: 'settings'.tr,
              )),
      ],
    );
  }

  Widget _buildEditForm() {
    return Card(
      margin: EdgeInsets.all(16.w),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'edit_profile'.tr,
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: 20.h),

              // Name field
              CustomTextField(
                controller: controller.nameController,
                labelText: 'name'.tr,
                prefixIcon: Icons.person,
                validator: controller.validateName,
                isRTL: true,
              ),
              SizedBox(height: 16.h),

              // Email field
              CustomTextField(
                controller: controller.emailController,
                labelText: 'email'.tr,
                prefixIcon: Icons.email,
                keyboardType: TextInputType.emailAddress,
                validator: controller.validateEmail,
                isRTL: true,
              ),
              SizedBox(height: 16.h),

              // Phone field
              CustomTextField(
                controller: controller.phoneController,
                labelText: 'phone'.tr,
                prefixIcon: Icons.phone,
                keyboardType: TextInputType.phone,
                validator: controller.validatePhone,
                isRTL: true,
              ),
              SizedBox(height: 16.h),

              // Address field
              CustomTextField(
                controller: controller.addressController,
                labelText: 'address'.tr,
                prefixIcon: Icons.location_on,
                maxLines: 2,
                validator: null,
                isRTL: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // Change password
          _buildActionCard(
            icon: Icons.lock,
            title: 'change_password'.tr,
            subtitle: 'update_your_password'.tr,
            onTap: controller.navigateToChangePassword,
            color: AppColors.warning,
          ),
          SizedBox(height: 12.h),

          // Settings
          _buildActionCard(
            icon: Icons.settings,
            title: 'settings'.tr,
            subtitle: 'app_preferences_and_configuration'.tr,
            onTap: controller.navigateToSettings,
            color: AppColors.info,
          ),
          SizedBox(height: 12.h),

          // Logout
          _buildActionCard(
            icon: Icons.logout,
            title: 'logout'.tr,
            subtitle: 'sign_out_of_your_account'.tr,
            onTap: controller.logout,
            color: AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurface,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.sp,
                color: AppColors.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'failed_to_load_profile'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.error,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: controller.loadUserProfile,
            child: Text('retry'.tr),
          ),
        ],
      ),
    );
  }
}
