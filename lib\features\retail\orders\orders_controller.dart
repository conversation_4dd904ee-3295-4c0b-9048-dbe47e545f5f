import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/order_service.dart';
import '../../../data/models/order.dart';
import '../../../core/utils/helpers.dart';

/// Controller for orders management
class OrdersController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();

  // Observable variables
  final isLoading = false.obs;
  final orders = <Order>[].obs;
  final filteredOrders = <Order>[].obs;
  final searchQuery = ''.obs;
  final selectedStatus = 'all'.obs;
  final selectedDateRange = 'all'.obs;

  // Filter options
  final statusOptions = [
    'all',
    'pending',
    'confirmed',
    'processing',
    'shipped',
    'delivered',
    'cancelled'
  ].obs;

  final dateRangeOptions = [
    'all',
    'today',
    'this_week',
    'this_month',
    'last_month'
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadOrders();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedDateRange, (_) => _applyFilters());
  }

  /// Load orders from service
  Future<void> loadOrders() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      orders.value = _generateMockOrders();
      
      // TODO: Replace with actual API call
      // orders.value = await _orderService.getOrders();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_orders'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = orders.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((order) {
        return order.orderNumber.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               order.customerName.toLowerCase().contains(searchQuery.value.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      filtered = filtered.where((order) => order.status == selectedStatus.value).toList();
    }

    // Apply date range filter
    if (selectedDateRange.value != 'all') {
      final now = DateTime.now();
      DateTime? startDate;

      switch (selectedDateRange.value) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case 'this_week':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          break;
        case 'this_month':
          startDate = DateTime(now.year, now.month, 1);
          break;
        case 'last_month':
          final lastMonth = DateTime(now.year, now.month - 1, 1);
          startDate = lastMonth;
          break;
      }

      if (startDate != null) {
        filtered = filtered.where((order) => order.orderDate.isAfter(startDate!)).toList();
      }
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.orderDate.compareTo(a.orderDate));

    filteredOrders.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update date range filter
  void updateDateRangeFilter(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
    selectedDateRange.value = 'all';
  }

  /// Navigate to order details
  void navigateToOrderDetails(String orderId) {
    Get.toNamed('/retail/orders/$orderId');
  }

  /// Navigate to create order
  void navigateToCreateOrder() {
    Get.toNamed('/retail/orders/create');
  }

  /// Navigate to edit order
  void navigateToEditOrder(String orderId) {
    Get.toNamed('/retail/orders/$orderId/edit');
  }

  /// Delete order
  Future<void> deleteOrder(String orderId) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_delete'.tr),
          content: Text('confirm_delete_order'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('cancel'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('delete'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to delete order
        // await _orderService.deleteOrder(orderId);
        
        // Remove from local list
        orders.removeWhere((order) => order.id == orderId);
        _applyFilters();
        
        Helpers.showSuccessSnackbar('order_deleted_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_delete_order'.tr);
    }
  }

  /// Refresh orders
  Future<void> refreshOrders() async {
    await loadOrders();
  }

  /// Generate mock orders for demonstration
  List<Order> _generateMockOrders() {
    return [
      Order(
        id: '1',
        orderNumber: 'ORD-001',
        customerName: 'محمد أحمد',
        customerId: 'CUST-001',
        orderDate: DateTime.now().subtract(const Duration(hours: 2)),
        status: 'pending',
        subtotal: 1150.00,
        taxAmount: 100.00,
        discountAmount: 0.00,
        shippingAmount: 0.00,
        totalAmount: 1250.00,
        paymentMethod: 'cash',
        paymentStatus: 'pending',
        items: [],
        notes: 'طلب عاجل',
        createdBy: 'USER-001',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      Order(
        id: '2',
        orderNumber: 'ORD-002',
        customerName: 'فاطمة علي',
        customerId: 'CUST-002',
        orderDate: DateTime.now().subtract(const Duration(days: 1)),
        status: 'confirmed',
        subtotal: 1950.00,
        taxAmount: 150.50,
        discountAmount: 0.00,
        shippingAmount: 0.00,
        totalAmount: 2100.50,
        paymentMethod: 'credit',
        paymentStatus: 'pending',
        items: [],
        notes: '',
        createdBy: 'USER-001',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 12)),
      ),
      Order(
        id: '3',
        orderNumber: 'ORD-003',
        customerName: 'عبدالله محمد',
        customerId: 'CUST-003',
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        status: 'delivered',
        subtotal: 800.00,
        taxAmount: 50.75,
        discountAmount: 0.00,
        shippingAmount: 0.00,
        totalAmount: 850.75,
        paymentMethod: 'cash',
        paymentStatus: 'paid',
        items: [],
        notes: 'تم التسليم بنجاح',
        createdBy: 'USER-001',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
}
