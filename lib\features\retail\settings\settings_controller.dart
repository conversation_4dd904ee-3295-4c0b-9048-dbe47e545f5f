import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/utils/helpers.dart';

/// Controller for app settings management
class SettingsController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();

  // Observable variables
  final isLoading = false.obs;
  final selectedLanguage = 'ar'.obs;
  final isDarkMode = false.obs;
  final notificationsEnabled = true.obs;
  final orderNotifications = true.obs;
  final visitNotifications = true.obs;
  final paymentNotifications = true.obs;
  final systemNotifications = true.obs;
  final soundEnabled = true.obs;
  final vibrationEnabled = true.obs;

  // Language options
  final languageOptions = [
    {'code': 'ar', 'name': 'العربية', 'flag': '🇸🇦'},
    {'code': 'en', 'name': 'English', 'flag': '🇺🇸'},
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }

  /// Load settings from storage
  Future<void> loadSettings() async {
    try {
      isLoading.value = true;

      // Load language setting
      final language = _storageService.getString('language') ?? 'ar';
      selectedLanguage.value = language;

      // Load theme setting
      final darkMode = _storageService.getBool('dark_mode') ?? false;
      isDarkMode.value = darkMode;

      // Load notification settings
      notificationsEnabled.value = _storageService.getBool('notifications_enabled') ?? true;
      orderNotifications.value = _storageService.getBool('order_notifications') ?? true;
      visitNotifications.value = _storageService.getBool('visit_notifications') ?? true;
      paymentNotifications.value = _storageService.getBool('payment_notifications') ?? true;
      systemNotifications.value = _storageService.getBool('system_notifications') ?? true;
      soundEnabled.value = _storageService.getBool('sound_enabled') ?? true;
      vibrationEnabled.value = _storageService.getBool('vibration_enabled') ?? true;

    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_settings'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Change app language
  Future<void> changeLanguage(String languageCode) async {
    try {
      selectedLanguage.value = languageCode;
      
      // Update locale
      final locale = Locale(languageCode);
      Get.updateLocale(locale);
      
      // Save to storage
      await _storageService.setString('language', languageCode);
      
      Helpers.showSuccessSnackbar('language_changed_successfully'.tr);
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_change_language'.tr);
    }
  }

  /// Toggle dark mode
  Future<void> toggleDarkMode(bool value) async {
    try {
      isDarkMode.value = value;
      
      // Update theme
      Get.changeThemeMode(value ? ThemeMode.dark : ThemeMode.light);
      
      // Save to storage
      await _storageService.setBool('dark_mode', value);
      
      Helpers.showSuccessSnackbar('theme_changed_successfully'.tr);
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_change_theme'.tr);
    }
  }

  /// Toggle notifications
  Future<void> toggleNotifications(bool value) async {
    try {
      notificationsEnabled.value = value;
      await _storageService.setBool('notifications_enabled', value);

      // If disabling all notifications, disable individual types too
      if (!value) {
        orderNotifications.value = false;
        visitNotifications.value = false;
        paymentNotifications.value = false;
        systemNotifications.value = false;
        await _storageService.setBool('order_notifications', false);
        await _storageService.setBool('visit_notifications', false);
        await _storageService.setBool('payment_notifications', false);
        await _storageService.setBool('system_notifications', false);
      }
      
      Helpers.showSuccessSnackbar('notification_settings_updated'.tr);
      
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_notifications'.tr);
    }
  }

  /// Toggle order notifications
  Future<void> toggleOrderNotifications(bool value) async {
    try {
      orderNotifications.value = value;
      await _storageService.setBool('order_notifications', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Toggle visit notifications
  Future<void> toggleVisitNotifications(bool value) async {
    try {
      visitNotifications.value = value;
      await _storageService.setBool('visit_notifications', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Toggle payment notifications
  Future<void> togglePaymentNotifications(bool value) async {
    try {
      paymentNotifications.value = value;
      await _storageService.setBool('payment_notifications', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Toggle system notifications
  Future<void> toggleSystemNotifications(bool value) async {
    try {
      systemNotifications.value = value;
      await _storageService.setBool('system_notifications', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Toggle sound
  Future<void> toggleSound(bool value) async {
    try {
      soundEnabled.value = value;
      await _storageService.setBool('sound_enabled', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Toggle vibration
  Future<void> toggleVibration(bool value) async {
    try {
      vibrationEnabled.value = value;
      await _storageService.setBool('vibration_enabled', value);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_update_settings'.tr);
    }
  }

  /// Clear app cache
  Future<void> clearCache() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_clear_cache'.tr),
          content: Text('confirm_clear_cache_message'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('cancel'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('clear'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Implement cache clearing logic
        // await _cacheService.clearCache();
        
        Helpers.showSuccessSnackbar('cache_cleared_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_clear_cache'.tr);
    }
  }

  /// Reset settings to default
  Future<void> resetSettings() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_reset_settings'.tr),
          content: Text('confirm_reset_settings_message'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('cancel'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('reset'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Reset to default values
        selectedLanguage.value = 'ar';
        isDarkMode.value = false;
        notificationsEnabled.value = true;
        orderNotifications.value = true;
        visitNotifications.value = true;
        paymentNotifications.value = true;
        systemNotifications.value = true;
        soundEnabled.value = true;
        vibrationEnabled.value = true;

        // Clear storage
        await _storageService.remove('language');
        await _storageService.remove('dark_mode');
        await _storageService.remove('notifications_enabled');
        await _storageService.remove('order_notifications');
        await _storageService.remove('visit_notifications');
        await _storageService.remove('payment_notifications');
        await _storageService.remove('system_notifications');
        await _storageService.remove('sound_enabled');
        await _storageService.remove('vibration_enabled');

        // Apply default settings
        Get.updateLocale(const Locale('ar'));
        Get.changeThemeMode(ThemeMode.light);

        Helpers.showSuccessSnackbar('settings_reset_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_reset_settings'.tr);
    }
  }

  /// Navigate to about screen
  void navigateToAbout() {
    Get.toNamed('/retail/about');
  }

  /// Navigate to privacy policy
  void navigateToPrivacyPolicy() {
    Get.toNamed('/retail/privacy-policy');
  }

  /// Navigate to terms of service
  void navigateToTermsOfService() {
    Get.toNamed('/retail/terms-of-service');
  }

  /// Get app version
  String get appVersion {
    return '1.0.0'; // TODO: Get from package info
  }

  /// Get build number
  String get buildNumber {
    return '1'; // TODO: Get from package info
  }
}
