# Al Ameen Sales App

A professional Flutter application for dual-role mobile sales management, serving both Retail and Wholesale Sales Representatives. This is a front-end implementation designed to consume REST APIs from a separate Laravel PHP backend.

## 🚀 Features

### Core Functionality
- **Dual Role Support**: Retail and Wholesale Sales Representatives
- **Token-based Authentication**: Secure login with automatic token refresh
- **Responsive Design**: Optimized for 5-7 inch mobile devices
- **Clean Architecture**: Feature-first organization with separation of concerns
- **State Management**: GetX for reactive programming and dependency injection

### Retail Representative Features (Full CRUD)
- **Orders**: Create, view, edit, and manage orders with product selection
- **Customers**: Complete customer management with profiles and history
- **Visits**: Schedule, track, and complete customer visits
- **Invoices**: Generate, send, and manage invoices with PDF export
- **Returns**: Handle return requests with approval workflow
- **Notifications**: Real-time notifications with filtering and management

### Wholesale Representative Features (Limited Operations)
- **Orders**: View and create basic orders
- **Customers**: View and add customers with basic information
- **Visits**: Schedule customer visits and view calendar
- **Notifications**: View relevant notifications

## 🏗️ Architecture

### Clean Architecture with Feature-First Organization

```
lib/
├── core/                   # Core utilities and configurations
│   ├── constants/         # App constants, API endpoints, strings
│   ├── themes/           # App theme, colors, text styles
│   ├── utils/            # Helpers, validators, formatters
│   ├── config/           # App configuration and environment settings
│   └── exceptions/       # Custom exceptions and error handling
├── data/                  # Data layer
│   ├── models/           # Data models with JSON serialization
│   ├── services/         # API services using GetConnect
│   └── repositories/     # Data repositories (future implementation)
├── features/             # Feature modules
│   ├── common/           # Shared features (auth, splash)
│   ├── retail/           # Retail representative features
│   └── wholesale/        # Wholesale representative features
├── routes/               # Navigation and routing
├── bindings/             # Dependency injection bindings
├── widgets/              # Reusable UI components
└── main.dart            # Application entry point
```

## 📱 Technology Stack

### Core Technologies
- **Flutter SDK**: 3.x (latest stable)
- **Dart**: 3.0+
- **GetX**: ^4.6.6 (State management, routing, dependency injection)

### Key Dependencies
- **flutter_screenutil**: ^5.9.0 (Responsive design)
- **google_fonts**: ^6.1.0 (Typography)
- **flutter_svg**: ^2.0.7 (Vector graphics)
- **flutter_secure_storage**: ^9.0.0 (Secure token storage)
- **flutter_animate**: ^4.2.0+1 (Animations)
- **intl**: ^0.19.0 (Internationalization)
- **json_annotation**: ^4.8.1 (JSON serialization)

### Development Dependencies
- **build_runner**: ^2.4.7 (Code generation)
- **json_serializable**: ^6.7.1 (JSON serialization)

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK 3.0 or higher
- Dart SDK 3.0 or higher
- Android Studio / VS Code with Flutter extensions
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/ENG-Hamzh-Khurd/AL-Ameen_App.git
   cd al_ameen_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code (for JSON serialization)**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**
   ```bash
   flutter run
   ```

## 🔐 Authentication

### Demo Login Credentials

The app includes demo login functionality for testing:

- **Retail Representative**:
  - Email: `<EMAIL>`
  - Password: `password123`

- **Wholesale Representative**:
  - Email: `<EMAIL>`
  - Password: `password123`

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: https://help.alameen.com

---

**Version**: 1.0.0
**Last Updated**: August 2024
**Developed by**: Al Ameen Development Team
#   A L - A m e e n _ A p p 
 
 