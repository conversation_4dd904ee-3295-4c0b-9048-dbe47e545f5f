import 'package:json_annotation/json_annotation.dart';

part 'invoice.g.dart';

/// Invoice model for Al Ameen Sales App
@JsonSerializable()
class Invoice {
  final String id;
  final String invoiceNumber;
  final String? orderId;
  final String customerId;
  final String customerName;
  final String status; // 'draft', 'sent', 'paid', 'overdue', 'cancelled'
  final DateTime invoiceDate;
  final DateTime dueDate;
  final List<InvoiceItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final double balanceAmount;
  final String paymentTerms;
  final String? notes;
  final String? customerAddress;
  final InvoiceSettings? settings;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? sentAt;
  final DateTime? paidAt;

  const Invoice({
    required this.id,
    required this.invoiceNumber,
    this.orderId,
    required this.customerId,
    required this.customerName,
    required this.status,
    required this.invoiceDate,
    required this.dueDate,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.balanceAmount,
    required this.paymentTerms,
    this.notes,
    this.customerAddress,
    this.settings,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.sentAt,
    this.paidAt,
  });

  /// Get formatted invoice number
  String get formattedInvoiceNumber => 'INV-$invoiceNumber';

  /// Check if invoice is draft
  bool get isDraft => status == 'draft';

  /// Check if invoice is sent
  bool get isSent => status == 'sent';

  /// Check if invoice is paid
  bool get isPaid => status == 'paid';

  /// Check if invoice is overdue
  bool get isOverdue => status == 'overdue' || (status == 'sent' && DateTime.now().isAfter(dueDate));

  /// Check if invoice is cancelled
  bool get isCancelled => status == 'cancelled';

  /// Check if invoice can be edited
  bool get canBeEdited => isDraft;

  /// Check if invoice can be sent
  bool get canBeSent => isDraft;

  /// Check if invoice can be cancelled
  bool get canBeCancelled => isDraft || isSent;

  /// Get payment percentage
  double get paymentPercentage {
    if (totalAmount == 0) return 0;
    return (paidAmount / totalAmount) * 100;
  }

  /// Compatibility getters for controllers
  DateTime? get sentDate => sentAt;
  DateTime? get paidDate => paidAt;

  /// Check if invoice is partially paid
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < totalAmount;

  /// Check if invoice is fully paid
  bool get isFullyPaid => paidAmount >= totalAmount;

  /// Get days until due
  int get daysUntilDue => dueDate.difference(DateTime.now()).inDays;

  /// Get days overdue
  int get daysOverdue {
    if (!isOverdue) return 0;
    return DateTime.now().difference(dueDate).inDays;
  }

  /// Get total quantity of items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  factory Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceToJson(this);

  Invoice copyWith({
    String? id,
    String? invoiceNumber,
    String? orderId,
    String? customerId,
    String? customerName,
    String? status,
    DateTime? invoiceDate,
    DateTime? dueDate,
    List<InvoiceItem>? items,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    double? balanceAmount,
    String? paymentTerms,
    String? notes,
    String? customerAddress,
    InvoiceSettings? settings,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? sentAt,
    DateTime? paidAt,
    // Compatibility parameters
    DateTime? sentDate,
    DateTime? paidDate,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      orderId: orderId ?? this.orderId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      status: status ?? this.status,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      dueDate: dueDate ?? this.dueDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      balanceAmount: balanceAmount ?? this.balanceAmount,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      notes: notes ?? this.notes,
      customerAddress: customerAddress ?? this.customerAddress,
      settings: settings ?? this.settings,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sentAt: sentDate ?? sentAt ?? this.sentAt,
      paidAt: paidDate ?? paidAt ?? this.paidAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Invoice && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Invoice(id: $id, number: $invoiceNumber, customer: $customerName, status: $status)';
}

/// Invoice item model
@JsonSerializable()
class InvoiceItem {
  final String id;
  final String productId;
  final String productName;
  final String? productCode;
  final String? description;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final double? discountAmount;
  final double? taxAmount;
  final String unit;

  const InvoiceItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productCode,
    this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountAmount,
    this.taxAmount,
    this.unit = 'pcs',
  });

  /// Get net price after discount
  double get netPrice => totalPrice - (discountAmount ?? 0);

  /// Get discount percentage
  double get discountPercentage {
    if (discountAmount == null || totalPrice == 0) return 0;
    return (discountAmount! / totalPrice) * 100;
  }

  factory InvoiceItem.fromJson(Map<String, dynamic> json) => _$InvoiceItemFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceItemToJson(this);

  InvoiceItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    String? description,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountAmount,
    double? taxAmount,
    String? unit,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      unit: unit ?? this.unit,
    );
  }
}

/// Invoice settings for customization
@JsonSerializable()
class InvoiceSettings {
  final String? logoUrl;
  final String? companyName;
  final String? companyAddress;
  final String? companyPhone;
  final String? companyEmail;
  final String? companyWebsite;
  final String? taxNumber;
  final String? bankDetails;
  final String? footerText;
  final String? currency;
  final String? template;

  const InvoiceSettings({
    this.logoUrl,
    this.companyName,
    this.companyAddress,
    this.companyPhone,
    this.companyEmail,
    this.companyWebsite,
    this.taxNumber,
    this.bankDetails,
    this.footerText,
    this.currency = 'USD',
    this.template = 'default',
  });

  factory InvoiceSettings.fromJson(Map<String, dynamic> json) => _$InvoiceSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceSettingsToJson(this);
}

/// Invoice filters for searching and filtering
class InvoiceFilters {
  final String? searchQuery;
  final List<String>? statuses;
  final String? customerId;
  final String? orderId;
  final DateTime? invoiceDateFrom;
  final DateTime? invoiceDateTo;
  final DateTime? dueDateFrom;
  final DateTime? dueDateTo;
  final double? minAmount;
  final double? maxAmount;
  final bool? isOverdue;
  final String? createdBy;
  final String? sortBy; // 'invoiceDate', 'dueDate', 'totalAmount', 'customerName'
  final String? sortOrder; // 'asc', 'desc'

  const InvoiceFilters({
    this.searchQuery,
    this.statuses,
    this.customerId,
    this.orderId,
    this.invoiceDateFrom,
    this.invoiceDateTo,
    this.dueDateFrom,
    this.dueDateTo,
    this.minAmount,
    this.maxAmount,
    this.isOverdue,
    this.createdBy,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery?.isNotEmpty == true) params['search'] = searchQuery;
    if (statuses?.isNotEmpty == true) params['statuses'] = statuses!.join(',');
    if (customerId?.isNotEmpty == true) params['customer_id'] = customerId;
    if (orderId?.isNotEmpty == true) params['order_id'] = orderId;
    if (invoiceDateFrom != null) params['invoice_date_from'] = invoiceDateFrom!.toIso8601String();
    if (invoiceDateTo != null) params['invoice_date_to'] = invoiceDateTo!.toIso8601String();
    if (dueDateFrom != null) params['due_date_from'] = dueDateFrom!.toIso8601String();
    if (dueDateTo != null) params['due_date_to'] = dueDateTo!.toIso8601String();
    if (minAmount != null) params['min_amount'] = minAmount;
    if (maxAmount != null) params['max_amount'] = maxAmount;
    if (isOverdue != null) params['is_overdue'] = isOverdue;
    if (createdBy?.isNotEmpty == true) params['created_by'] = createdBy;
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}
