import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/notification.dart' as models;

/// Notification card widget for displaying notification information
class NotificationCard extends StatelessWidget {
  final models.Notification notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkRead;
  final VoidCallback? onMarkUnread;
  final VoidCallback? onDelete;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkRead,
    this.onMarkUnread,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: notification.isRead ? 1 : 3,
        margin: EdgeInsets.only(bottom: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: notification.isRead 
                ? AppColors.surface 
                : AppColors.primary.withValues(alpha: 0.05),
              border: notification.isHighPriority 
                ? Border.all(
                    color: notification.isUrgent ? AppColors.error : AppColors.warning,
                    width: 1,
                  )
                : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    // Notification icon
                    _buildNotificationIcon(),
                    
                    SizedBox(width: 12.w),
                    
                    // Title and content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  notification.title,
                                  style: AppTextStyles.titleSmall.copyWith(
                                    fontWeight: notification.isRead 
                                      ? FontWeight.w500 
                                      : FontWeight.bold,
                                    color: AppColors.onSurface,
                                  ),
                                ),
                              ),
                              
                              // Priority indicator
                              if (notification.isHighPriority) ...[
                                SizedBox(width: 8.w),
                                _buildPriorityChip(),
                              ],
                            ],
                          ),
                          
                          SizedBox(height: 4.h),
                          
                          Text(
                            notification.message,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                              fontWeight: notification.isRead 
                                ? FontWeight.normal 
                                : FontWeight.w500,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    _buildActionsMenu(),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Footer with time and type
                Row(
                  children: [
                    // Type chip
                    _buildTypeChip(),
                    
                    const Spacer(),
                    
                    // Time ago
                    Text(
                      _getTimeAgo(),
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                    
                    // Unread indicator
                    if (!notification.isRead) ...[
                      SizedBox(width: 8.w),
                      Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case 'order':
        iconData = Icons.shopping_cart;
        iconColor = AppColors.primary;
        break;
      case 'visit':
        iconData = Icons.event;
        iconColor = AppColors.info;
        break;
      case 'invoice':
        iconData = Icons.receipt;
        iconColor = AppColors.success;
        break;
      case 'return':
        iconData = Icons.assignment_return;
        iconColor = AppColors.warning;
        break;
      case 'reminder':
        iconData = Icons.alarm;
        iconColor = AppColors.warning;
        break;
      case 'alert':
        iconData = Icons.warning;
        iconColor = AppColors.error;
        break;
      case 'system':
        iconData = Icons.settings;
        iconColor = AppColors.grey500;
        break;
      case 'general':
      default:
        iconData = Icons.notifications;
        iconColor = AppColors.primary;
    }

    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        iconData,
        size: 20.sp,
        color: iconColor,
      ),
    );
  }

  Widget _buildPriorityChip() {
    Color priorityColor;
    String priorityText;

    if (notification.isUrgent) {
      priorityColor = AppColors.error;
      priorityText = 'urgent'.tr;
    } else {
      priorityColor = AppColors.warning;
      priorityText = 'high'.tr;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        priorityText,
        style: AppTextStyles.labelSmall.copyWith(
          color: priorityColor,
          fontWeight: FontWeight.w600,
          fontSize: 10.sp,
        ),
      ),
    );
  }

  Widget _buildTypeChip() {
    String typeText;
    Color typeColor;

    switch (notification.type) {
      case 'order':
        typeText = 'order'.tr;
        typeColor = AppColors.primary;
        break;
      case 'visit':
        typeText = 'visit'.tr;
        typeColor = AppColors.info;
        break;
      case 'invoice':
        typeText = 'invoice'.tr;
        typeColor = AppColors.success;
        break;
      case 'return':
        typeText = 'return'.tr;
        typeColor = AppColors.warning;
        break;
      case 'reminder':
        typeText = 'reminder'.tr;
        typeColor = AppColors.warning;
        break;
      case 'alert':
        typeText = 'alert'.tr;
        typeColor = AppColors.error;
        break;
      case 'system':
        typeText = 'system'.tr;
        typeColor = AppColors.grey500;
        break;
      case 'general':
      default:
        typeText = 'general'.tr;
        typeColor = AppColors.primary;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: typeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        typeText,
        style: AppTextStyles.labelSmall.copyWith(
          color: typeColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionsMenu() {
    final actions = <PopupMenuItem<String>>[];

    if (notification.isRead && onMarkUnread != null) {
      actions.add(
        PopupMenuItem(
          value: 'mark_unread',
          child: Row(
            children: [
              Icon(Icons.mark_email_unread, size: 18.sp),
              SizedBox(width: 8.w),
              Text('mark_as_unread'.tr),
            ],
          ),
        ),
      );
    } else if (!notification.isRead && onMarkRead != null) {
      actions.add(
        PopupMenuItem(
          value: 'mark_read',
          child: Row(
            children: [
              Icon(Icons.mark_email_read, size: 18.sp),
              SizedBox(width: 8.w),
              Text('mark_as_read'.tr),
            ],
          ),
        ),
      );
    }

    if (onDelete != null) {
      actions.add(
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text('delete'.tr),
            ],
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'mark_read':
            onMarkRead?.call();
            break;
          case 'mark_unread':
            onMarkUnread?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => actions,
      child: Icon(
        Icons.more_vert,
        color: AppColors.onSurfaceVariant,
        size: 20.sp,
      ),
    );
  }

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(notification.createdAt);

    if (difference.inMinutes < 1) {
      return 'just_now'.tr;
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${'minutes_ago'.tr}';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${'hours_ago'.tr}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ${'days_ago'.tr}';
    } else {
      return FormatUtils.formatDate(notification.createdAt);
    }
  }
}
