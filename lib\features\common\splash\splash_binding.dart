import 'package:get/get.dart';
import 'splash_controller.dart';
import '../../../data/services/auth_service.dart';

/// Splash screen binding for dependency injection
class SplashBinding extends Bindings {
  @override
  void dependencies() {
    // Register AuthService if not already registered
    if (!Get.isRegistered<AuthService>()) {
      Get.put<AuthService>(AuthService(), permanent: true);
    }
    
    // Register SplashController
    Get.lazyPut<SplashController>(() => SplashController());
  }
}
