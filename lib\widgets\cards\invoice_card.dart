import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/invoice.dart';

/// Invoice card widget for displaying invoice information
class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onSend;
  final VoidCallback? onMarkPaid;
  final VoidCallback? onCancel;
  final VoidCallback? onPrint;
  final VoidCallback? onShare;

  const InvoiceCard({
    super.key,
    required this.invoice,
    this.onTap,
    this.onEdit,
    this.onSend,
    this.onMarkPaid,
    this.onCancel,
    this.onPrint,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: invoice.isOverdue 
                ? Border.all(color: AppColors.error, width: 1)
                : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            invoice.invoiceNumber,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            invoice.customerName,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Status chip
                    _buildStatusChip(),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    _buildActionsMenu(),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Invoice details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.calendar_today,
                        label: 'invoice_date'.tr,
                        value: FormatUtils.formatDate(invoice.invoiceDate),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.event,
                        label: 'due_date'.tr,
                        value: FormatUtils.formatDate(invoice.dueDate),
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Amount details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.attach_money,
                        label: 'total_amount'.tr,
                        value: FormatUtils.formatCurrency(invoice.totalAmount),
                      ),
                    ),
                    if (invoice.paidAmount > 0) ...[
                      SizedBox(width: 16.w),
                      Expanded(
                        child: _buildDetailItem(
                          icon: Icons.payment,
                          label: 'paid_amount'.tr,
                          value: FormatUtils.formatCurrency(invoice.paidAmount),
                        ),
                      ),
                    ],
                  ],
                ),
                
                if (invoice.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 12.h),
                  _buildDetailItem(
                    icon: Icons.note,
                    label: 'notes'.tr,
                    value: invoice.notes!,
                  ),
                ],
                
                // Payment status for overdue invoices
                if (invoice.isOverdue) ...[
                  SizedBox(height: 12.h),
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: AppColors.error,
                          size: 16.sp,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            'invoice_overdue_warning'.tr,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (invoice.status) {
      case 'draft':
        statusColor = AppColors.grey500;
        statusText = 'draft'.tr;
        statusIcon = Icons.edit;
        break;
      case 'sent':
        statusColor = AppColors.info;
        statusText = 'sent'.tr;
        statusIcon = Icons.send;
        break;
      case 'paid':
        statusColor = AppColors.success;
        statusText = 'paid'.tr;
        statusIcon = Icons.check_circle;
        break;
      case 'overdue':
        statusColor = AppColors.error;
        statusText = 'overdue'.tr;
        statusIcon = Icons.warning;
        break;
      case 'cancelled':
        statusColor = AppColors.error;
        statusText = 'cancelled'.tr;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = AppColors.grey500;
        statusText = invoice.status;
        statusIcon = Icons.help;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 14.sp,
            color: statusColor,
          ),
          SizedBox(width: 4.w),
          Text(
            statusText,
            style: AppTextStyles.labelSmall.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsMenu() {
    final actions = <PopupMenuItem<String>>[];

    // Add actions based on invoice status
    if (invoice.status == 'draft' && onSend != null) {
      actions.add(
        PopupMenuItem(
          value: 'send',
          child: Row(
            children: [
              Icon(Icons.send, size: 18.sp, color: AppColors.primary),
              SizedBox(width: 8.w),
              Text('send_invoice'.tr),
            ],
          ),
        ),
      );
    }

    if ((invoice.status == 'sent' || invoice.status == 'overdue') && onMarkPaid != null) {
      actions.add(
        PopupMenuItem(
          value: 'mark_paid',
          child: Row(
            children: [
              Icon(Icons.payment, size: 18.sp, color: AppColors.success),
              SizedBox(width: 8.w),
              Text('mark_as_paid'.tr),
            ],
          ),
        ),
      );
    }

    if (onPrint != null) {
      actions.add(
        PopupMenuItem(
          value: 'print',
          child: Row(
            children: [
              Icon(Icons.print, size: 18.sp),
              SizedBox(width: 8.w),
              Text('print_invoice'.tr),
            ],
          ),
        ),
      );
    }

    if (onShare != null) {
      actions.add(
        PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share, size: 18.sp),
              SizedBox(width: 8.w),
              Text('share_invoice'.tr),
            ],
          ),
        ),
      );
    }

    if (onEdit != null && invoice.status == 'draft') {
      actions.add(
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18.sp),
              SizedBox(width: 8.w),
              Text('edit'.tr),
            ],
          ),
        ),
      );
    }

    if (onCancel != null && invoice.status != 'paid' && invoice.status != 'cancelled') {
      actions.add(
        PopupMenuItem(
          value: 'cancel',
          child: Row(
            children: [
              Icon(Icons.cancel, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text('cancel'.tr),
            ],
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'send':
            onSend?.call();
            break;
          case 'mark_paid':
            onMarkPaid?.call();
            break;
          case 'print':
            onPrint?.call();
            break;
          case 'share':
            onShare?.call();
            break;
          case 'edit':
            onEdit?.call();
            break;
          case 'cancel':
            onCancel?.call();
            break;
        }
      },
      itemBuilder: (context) => actions,
      child: Icon(
        Icons.more_vert,
        color: AppColors.onSurfaceVariant,
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: AppColors.onSurfaceVariant,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
