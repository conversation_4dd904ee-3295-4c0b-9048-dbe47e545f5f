import 'package:json_annotation/json_annotation.dart';

part 'customer.g.dart';

/// Customer model for Al Ameen Sales App
@JsonSerializable()
class Customer {
  final String id;
  final String customerCode;
  final String name;
  final String? email;
  final String? phone;
  final String? contactPerson;
  final String type; // 'retail', 'wholesale', 'distributor'
  final String status; // 'active', 'inactive', 'suspended'
  final CustomerAddress? address;
  final String? notes;
  final double creditLimit;
  final double currentBalance;
  final String paymentTerms; // 'cash', 'net30', 'net60', etc.
  final String? taxId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final DateTime? lastOrderDate;
  final double totalOrderValue;
  final int totalOrders;

  const Customer({
    required this.id,
    required this.customerCode,
    required this.name,
    this.email,
    this.phone,
    this.contactPerson,
    required this.type,
    required this.status,
    this.address,
    this.notes,
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.paymentTerms = 'cash',
    this.taxId,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.lastOrderDate,
    this.totalOrderValue = 0.0,
    this.totalOrders = 0,
  });

  /// Check if customer is active
  bool get isActive => status == 'active';

  /// Check if customer is retail type
  bool get isRetail => type == 'retail';

  /// Check if customer is wholesale type
  bool get isWholesale => type == 'wholesale';

  /// Get available credit
  double get availableCredit => creditLimit - currentBalance;

  /// Check if customer has exceeded credit limit
  bool get isOverCreditLimit => currentBalance > creditLimit;

  /// Get customer display name (contact person if available, otherwise name)
  String get displayName => contactPerson?.isNotEmpty == true ? contactPerson! : name;

  /// Get formatted customer code
  String get formattedCode => 'CUST-$customerCode';

  factory Customer.fromJson(Map<String, dynamic> json) => _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  Customer copyWith({
    String? id,
    String? customerCode,
    String? name,
    String? email,
    String? phone,
    String? contactPerson,
    String? type,
    String? status,
    CustomerAddress? address,
    String? notes,
    double? creditLimit,
    double? currentBalance,
    String? paymentTerms,
    String? taxId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    DateTime? lastOrderDate,
    double? totalOrderValue,
    int? totalOrders,
  }) {
    return Customer(
      id: id ?? this.id,
      customerCode: customerCode ?? this.customerCode,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      contactPerson: contactPerson ?? this.contactPerson,
      type: type ?? this.type,
      status: status ?? this.status,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      taxId: taxId ?? this.taxId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      lastOrderDate: lastOrderDate ?? this.lastOrderDate,
      totalOrderValue: totalOrderValue ?? this.totalOrderValue,
      totalOrders: totalOrders ?? this.totalOrders,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Customer && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Customer(id: $id, code: $customerCode, name: $name, type: $type)';
}

/// Customer address information
@JsonSerializable()
class CustomerAddress {
  final String? street;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;
  final double? latitude;
  final double? longitude;
  final bool isDefault;

  const CustomerAddress({
    this.street,
    this.city,
    this.state,
    this.zipCode,
    this.country,
    this.latitude,
    this.longitude,
    this.isDefault = true,
  });

  /// Get postal code (alias for zipCode for compatibility)
  String? get postalCode => zipCode;

  /// Get formatted address
  String get formattedAddress {
    final parts = <String>[];
    if (street?.isNotEmpty == true) parts.add(street!);
    if (city?.isNotEmpty == true) parts.add(city!);
    if (state?.isNotEmpty == true) parts.add(state!);
    if (zipCode?.isNotEmpty == true) parts.add(zipCode!);
    if (country?.isNotEmpty == true) parts.add(country!);
    return parts.join(', ');
  }

  /// Check if address has coordinates
  bool get hasCoordinates => latitude != null && longitude != null;

  factory CustomerAddress.fromJson(Map<String, dynamic> json) => _$CustomerAddressFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerAddressToJson(this);

  CustomerAddress copyWith({
    String? street,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    double? latitude,
    double? longitude,
    bool? isDefault,
  }) {
    return CustomerAddress(
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isDefault: isDefault ?? this.isDefault,
    );
  }
}

/// Customer statistics
@JsonSerializable()
class CustomerStats {
  final String customerId;
  final int totalOrders;
  final double totalOrderValue;
  final double averageOrderValue;
  final int totalVisits;
  final DateTime? lastOrderDate;
  final DateTime? lastVisitDate;
  final String? preferredPaymentMethod;
  final List<String> topProducts;
  final double yearToDateValue;
  final double lastYearValue;
  final double growthPercentage;

  const CustomerStats({
    required this.customerId,
    this.totalOrders = 0,
    this.totalOrderValue = 0.0,
    this.averageOrderValue = 0.0,
    this.totalVisits = 0,
    this.lastOrderDate,
    this.lastVisitDate,
    this.preferredPaymentMethod,
    this.topProducts = const [],
    this.yearToDateValue = 0.0,
    this.lastYearValue = 0.0,
    this.growthPercentage = 0.0,
  });

  factory CustomerStats.fromJson(Map<String, dynamic> json) => _$CustomerStatsFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerStatsToJson(this);
}

/// Customer search filters
class CustomerFilters {
  final String? searchQuery;
  final List<String>? types;
  final List<String>? statuses;
  final String? city;
  final String? state;
  final double? minCreditLimit;
  final double? maxCreditLimit;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? lastOrderAfter;
  final DateTime? lastOrderBefore;
  final String? sortBy; // 'name', 'createdAt', 'lastOrderDate', 'totalOrderValue'
  final String? sortOrder; // 'asc', 'desc'

  const CustomerFilters({
    this.searchQuery,
    this.types,
    this.statuses,
    this.city,
    this.state,
    this.minCreditLimit,
    this.maxCreditLimit,
    this.createdAfter,
    this.createdBefore,
    this.lastOrderAfter,
    this.lastOrderBefore,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery?.isNotEmpty == true) params['search'] = searchQuery;
    if (types?.isNotEmpty == true) params['types'] = types!.join(',');
    if (statuses?.isNotEmpty == true) params['statuses'] = statuses!.join(',');
    if (city?.isNotEmpty == true) params['city'] = city;
    if (state?.isNotEmpty == true) params['state'] = state;
    if (minCreditLimit != null) params['min_credit_limit'] = minCreditLimit;
    if (maxCreditLimit != null) params['max_credit_limit'] = maxCreditLimit;
    if (createdAfter != null) params['created_after'] = createdAfter!.toIso8601String();
    if (createdBefore != null) params['created_before'] = createdBefore!.toIso8601String();
    if (lastOrderAfter != null) params['last_order_after'] = lastOrderAfter!.toIso8601String();
    if (lastOrderBefore != null) params['last_order_before'] = lastOrderBefore!.toIso8601String();
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}
