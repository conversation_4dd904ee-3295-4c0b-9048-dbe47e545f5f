# Al Ameen Sales App - Reusable Components Library

This document provides a comprehensive overview of all reusable UI components created for the Al Ameen Sales App. All components are designed with Arabic RTL layout support and follow consistent theming and styling patterns.

## Table of Contents

1. [Cards](#cards)
2. [Forms](#forms)
3. [Navigation](#navigation)
4. [Buttons](#buttons)
5. [Utilities](#utilities)
6. [Design Principles](#design-principles)
7. [Usage Guidelines](#usage-guidelines)

## Cards

### 1. CustomerCard (`lib/widgets/cards/customer_card.dart`)
**Purpose:** Display customer information with status indicators and actions
**Features:**
- Customer name, code, and contact information
- Status chips (active/inactive) with color coding
- Type indicators (retail/wholesale/distributor)
- Balance and credit limit display
- RTL-compliant layout
- Action menu with edit/delete options

**Usage:**
```dart
CustomerCard(
  customer: customer,
  onTap: () => controller.navigateToCustomerDetails(customer.id),
  onEdit: () => controller.navigateToEditCustomer(customer.id),
  onDelete: () => controller.deleteCustomer(customer.id),
)
```

### 2. OrderCard (`lib/widgets/cards/order_card.dart`)
**Purpose:** Display order information with status tracking and actions
**Features:**
- Order number, customer name, and date
- Status indicators with color coding
- Total amount and item count
- Progress indicators for order stages
- Action buttons for status updates
- RTL-compliant layout

### 3. VisitCard (`lib/widgets/cards/visit_card.dart`)
**Purpose:** Display visit information with scheduling and status management
**Features:**
- Customer name and visit purpose
- Scheduled date and time
- Status indicators (scheduled, in_progress, completed, cancelled)
- Location information
- Action buttons for visit management
- Overdue warnings

### 4. InvoiceCard (`lib/widgets/cards/invoice_card.dart`)
**Purpose:** Display invoice information with payment tracking
**Features:**
- Invoice number and customer name
- Invoice and due dates
- Total and paid amounts
- Payment status indicators
- Overdue warnings
- Action menu for invoice operations

### 5. ReturnCard (`lib/widgets/cards/return_card.dart`)
**Purpose:** Display return information with approval workflow
**Features:**
- Return number and customer name
- Return reason and amount
- Status indicators (requested, approved, rejected, processing)
- Approval workflow buttons
- Rejection reason display

### 6. NotificationCard (`lib/widgets/cards/notification_card.dart`)
**Purpose:** Display notification information with read/unread status
**Features:**
- Notification title and message
- Type indicators with icons
- Priority indicators (urgent, high)
- Read/unread status
- Time ago formatting
- Action menu for notification management

### 7. ProfileCard (`lib/widgets/cards/profile_card.dart`)
**Purpose:** Display user profile information with statistics
**Features:**
- User avatar with fallback
- Name, email, and contact information
- Role and department display
- User statistics section
- Member since information
- Action buttons for editing and settings

## Forms

### 1. CustomTextField (`lib/widgets/forms/custom_text_field.dart`)
**Purpose:** Standardized text input field with Arabic RTL support
**Features:**
- RTL text direction support
- Consistent styling with app theme
- Icon support (prefix/suffix)
- Validation support
- Multiple input types (text, email, phone, password)
- Multi-line support

**Usage:**
```dart
CustomTextField(
  labelText: 'customer_name'.tr,
  prefixIcon: Icons.person,
  validator: (value) => value?.isEmpty == true ? 'name_required'.tr : null,
  isRTL: true,
)
```

### 2. CustomDropdown (`lib/widgets/forms/custom_dropdown.dart`)
**Purpose:** Standardized dropdown field with Arabic RTL support
**Features:**
- RTL layout support
- Consistent styling
- Search functionality
- Custom item builders
- Validation support

### 3. CustomButton (`lib/widgets/forms/custom_button.dart`)
**Purpose:** Standardized button component with loading states
**Features:**
- Multiple button styles (primary, secondary, outline)
- Loading state support
- Icon support
- Consistent sizing and styling
- RTL icon positioning

## Navigation

### 1. CustomBottomNavigation (`lib/widgets/navigation/custom_bottom_navigation.dart`)
**Purpose:** Bottom navigation bar with Arabic labels and RTL support
**Features:**
- Arabic labels with proper RTL layout
- Active/inactive state indicators
- Badge support for notifications
- Consistent styling with app theme

### 2. CustomAppBar (`lib/widgets/navigation/custom_app_bar.dart`)
**Purpose:** Standardized app bar with Arabic RTL support
**Features:**
- RTL layout support
- Consistent styling
- Action button support
- Title and subtitle support

## Buttons

### 1. ActionButton (`lib/widgets/buttons/action_button.dart`)
**Purpose:** Standardized action button with consistent styling
**Features:**
- Multiple sizes (small, medium, large)
- Icon support
- Loading states
- Disabled states
- RTL icon positioning

### 2. FloatingActionButton (`lib/widgets/buttons/floating_action_button.dart`)
**Purpose:** Customized floating action button
**Features:**
- Consistent styling with app theme
- Icon support
- Extended FAB support
- RTL positioning

## Utilities

### 1. LoadingWidget (`lib/widgets/utils/loading_widget.dart`)
**Purpose:** Standardized loading indicator
**Features:**
- Consistent styling
- Multiple sizes
- Optional message display
- RTL text support

### 2. EmptyStateWidget (`lib/widgets/utils/empty_state_widget.dart`)
**Purpose:** Standardized empty state display
**Features:**
- Icon and message display
- Action button support
- RTL text layout
- Consistent styling

### 3. ErrorWidget (`lib/widgets/utils/error_widget.dart`)
**Purpose:** Standardized error state display
**Features:**
- Error message display
- Retry button support
- RTL text layout
- Consistent styling

## Design Principles

### 1. RTL Support
- All components support Arabic RTL text direction
- Icons and layouts are properly positioned for RTL
- Text alignment follows RTL conventions

### 2. Consistent Theming
- All components use the app's color scheme
- Typography follows the Tajawal font family
- Spacing and sizing use consistent scale

### 3. Accessibility
- Proper semantic labels
- Sufficient color contrast
- Touch target sizes meet accessibility guidelines

### 4. Responsive Design
- Components adapt to different screen sizes
- Use of ScreenUtil for consistent scaling
- Flexible layouts that work on various devices

## Usage Guidelines

### 1. Naming Conventions
- Use descriptive names that indicate purpose
- Follow camelCase for file names
- Use PascalCase for class names

### 2. Component Structure
- Keep components focused on single responsibility
- Use composition over inheritance
- Provide clear and documented APIs

### 3. Styling
- Use theme colors instead of hardcoded values
- Follow consistent spacing patterns
- Use semantic color names (primary, secondary, etc.)

### 4. Internationalization
- All text should use translation keys
- Support both Arabic and English languages
- Handle RTL layout differences properly

### 5. Performance
- Use const constructors where possible
- Avoid unnecessary rebuilds
- Extract complex logic to controllers

## Component Categories Summary

| Category | Components | Purpose |
|----------|------------|---------|
| Cards | 7 components | Display structured information with actions |
| Forms | 3 components | User input and data collection |
| Navigation | 2 components | App navigation and structure |
| Buttons | 2 components | User actions and interactions |
| Utilities | 3 components | Common UI patterns and states |

## Total Components: 17

All components are designed to work seamlessly together and provide a consistent user experience throughout the Al Ameen Sales App.
