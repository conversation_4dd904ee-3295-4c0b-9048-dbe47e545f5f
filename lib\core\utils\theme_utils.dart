import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../themes/app_colors.dart';
import '../themes/text_styles.dart';

/// Utility functions for consistent theming throughout the app
class ThemeUtils {
  ThemeUtils._(); // Prevent instantiation

  /// Get card decoration with consistent styling
  static BoxDecoration getCardDecoration({
    Color? color,
    double? borderRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.surface,
      borderRadius: BorderRadius.circular(borderRadius?.r ?? 12.r),
      boxShadow: boxShadow ?? [
        BoxShadow(
          color: AppColors.shadowLight,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
      border: border,
    );
  }

  /// Get elevated card decoration
  static BoxDecoration getElevatedCardDecoration({
    Color? color,
    double? borderRadius,
    double? elevation,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.surface,
      borderRadius: BorderRadius.circular(borderRadius?.r ?? 12.r),
      boxShadow: [
        BoxShadow(
          color: AppColors.shadowLight,
          blurRadius: (elevation ?? 4) * 2,
          offset: Offset(0, elevation ?? 4),
        ),
      ],
    );
  }

  /// Get input decoration for text fields
  static InputDecoration getInputDecoration({
    String? labelText,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    Widget? suffixWidget,
    bool isError = false,
    bool isRTL = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon != null 
          ? Icon(prefixIcon, color: AppColors.onSurfaceVariant) 
          : null,
      suffixIcon: suffixWidget ?? (suffixIcon != null 
          ? Icon(suffixIcon, color: AppColors.onSurfaceVariant) 
          : null),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(color: AppColors.borderLight),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(color: AppColors.borderLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(color: AppColors.error, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(color: AppColors.error, width: 2),
      ),
      filled: true,
      fillColor: AppColors.surface,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      alignLabelWithHint: true,
    );
  }

  /// Get button style for elevated buttons
  static ButtonStyle getElevatedButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    double? borderRadius,
    EdgeInsets? padding,
    Size? minimumSize,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.onPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius?.r ?? 8.r),
      ),
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      minimumSize: minimumSize ?? Size(0, 44.h),
      elevation: 2,
    );
  }

  /// Get button style for outlined buttons
  static ButtonStyle getOutlinedButtonStyle({
    Color? borderColor,
    Color? foregroundColor,
    double? borderRadius,
    EdgeInsets? padding,
    Size? minimumSize,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: foregroundColor ?? AppColors.primary,
      side: BorderSide(color: borderColor ?? AppColors.primary),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius?.r ?? 8.r),
      ),
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      minimumSize: minimumSize ?? Size(0, 44.h),
    );
  }

  /// Get button style for text buttons
  static ButtonStyle getTextButtonStyle({
    Color? foregroundColor,
    double? borderRadius,
    EdgeInsets? padding,
    Size? minimumSize,
  }) {
    return TextButton.styleFrom(
      foregroundColor: foregroundColor ?? AppColors.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius?.r ?? 8.r),
      ),
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      minimumSize: minimumSize ?? Size(0, 44.h),
    );
  }

  /// Get chip decoration
  static BoxDecoration getChipDecoration({
    required Color color,
    double? borderRadius,
    bool hasBorder = true,
  }) {
    return BoxDecoration(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(borderRadius?.r ?? 12.r),
      border: hasBorder 
          ? Border.all(color: color.withValues(alpha: 0.3))
          : null,
    );
  }

  /// Get status color based on status string
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'paid':
      case 'approved':
      case 'successful':
        return AppColors.success;
      case 'pending':
      case 'in_progress':
      case 'processing':
      case 'scheduled':
        return AppColors.warning;
      case 'inactive':
      case 'cancelled':
      case 'rejected':
      case 'failed':
      case 'overdue':
        return AppColors.error;
      case 'draft':
      case 'sent':
        return AppColors.info;
      default:
        return AppColors.grey500;
    }
  }

  /// Get priority color based on priority string
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'urgent':
      case 'critical':
        return AppColors.error;
      case 'high':
        return AppColors.warning;
      case 'medium':
      case 'normal':
        return AppColors.info;
      case 'low':
        return AppColors.success;
      default:
        return AppColors.grey500;
    }
  }

  /// Get icon color based on type
  static Color getIconColor(String type) {
    switch (type.toLowerCase()) {
      case 'order':
      case 'orders':
        return AppColors.primary;
      case 'customer':
      case 'customers':
        return AppColors.info;
      case 'visit':
      case 'visits':
        return AppColors.warning;
      case 'invoice':
      case 'invoices':
        return AppColors.success;
      case 'return':
      case 'returns':
        return AppColors.error;
      case 'notification':
      case 'notifications':
        return AppColors.primary;
      default:
        return AppColors.onSurfaceVariant;
    }
  }

  /// Get gradient decoration
  static BoxDecoration getGradientDecoration({
    required List<Color> colors,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    double? borderRadius,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: begin,
        end: end,
        colors: colors,
      ),
      borderRadius: borderRadius != null 
          ? BorderRadius.circular(borderRadius.r)
          : null,
    );
  }

  /// Get shimmer decoration for loading states
  static BoxDecoration getShimmerDecoration({
    double? borderRadius,
  }) {
    return BoxDecoration(
      color: AppColors.grey200,
      borderRadius: borderRadius != null 
          ? BorderRadius.circular(borderRadius.r)
          : null,
    );
  }

  /// Get divider with consistent styling
  static Widget getDivider({
    double? height,
    double? thickness,
    Color? color,
    double? indent,
    double? endIndent,
  }) {
    return Divider(
      height: height?.h ?? 1.h,
      thickness: thickness?.h ?? 1.h,
      color: color ?? AppColors.borderLight,
      indent: indent?.w,
      endIndent: endIndent?.w,
    );
  }

  /// Get vertical divider with consistent styling
  static Widget getVerticalDivider({
    double? width,
    double? thickness,
    Color? color,
    double? indent,
    double? endIndent,
  }) {
    return VerticalDivider(
      width: width?.w ?? 1.w,
      thickness: thickness?.w ?? 1.w,
      color: color ?? AppColors.borderLight,
      indent: indent?.h,
      endIndent: endIndent?.h,
    );
  }

  /// Get consistent spacing
  static SizedBox getVerticalSpacing(double height) {
    return SizedBox(height: height.h);
  }

  /// Get consistent horizontal spacing
  static SizedBox getHorizontalSpacing(double width) {
    return SizedBox(width: width.w);
  }

  /// Get consistent padding
  static EdgeInsets getPadding({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    if (all != null) {
      return EdgeInsets.all(all.w);
    }
    
    return EdgeInsets.only(
      top: (top ?? vertical ?? 0).h,
      bottom: (bottom ?? vertical ?? 0).h,
      left: (left ?? horizontal ?? 0).w,
      right: (right ?? horizontal ?? 0).w,
    );
  }

  /// Get consistent margin
  static EdgeInsets getMargin({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    if (all != null) {
      return EdgeInsets.all(all.w);
    }
    
    return EdgeInsets.only(
      top: (top ?? vertical ?? 0).h,
      bottom: (bottom ?? vertical ?? 0).h,
      left: (left ?? horizontal ?? 0).w,
      right: (right ?? horizontal ?? 0).w,
    );
  }

  /// Get text style with responsive sizing
  static TextStyle getResponsiveTextStyle({
    required TextStyle baseStyle,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return baseStyle.copyWith(
      fontSize: fontSize?.sp ?? baseStyle.fontSize,
      fontWeight: fontWeight ?? baseStyle.fontWeight,
      color: color ?? baseStyle.color,
      height: height ?? baseStyle.height,
    );
  }

  /// Get app bar theme
  static AppBarTheme getAppBarTheme() {
    return AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: AppTextStyles.titleLarge.copyWith(
        color: AppColors.onPrimary,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// Get bottom navigation bar theme
  static BottomNavigationBarThemeData getBottomNavigationBarTheme() {
    return BottomNavigationBarThemeData(
      backgroundColor: AppColors.surface,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTextStyles.labelSmall,
    );
  }
}
