import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'customers_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/cards/customer_card.dart';

/// Customers list screen for retail representatives
class CustomersScreen extends GetView<CustomersController> {
  const CustomersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            // Search and filters
            _buildSearchAndFilters(),
            
            // Customers list
            Expanded(
              child: _buildCustomersList(),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('customers'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: controller.clearFilters,
          icon: const Icon(Icons.clear_all),
          tooltip: 'clear_filters'.tr,
        ),
        IconButton(
          onPressed: controller.refreshCustomers,
          icon: const Icon(Icons.refresh),
          tooltip: 'refresh'.tr,
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field
          CustomTextField(
            hintText: 'search_customers'.tr,
            prefixIcon: Icons.search,
            onChanged: controller.updateSearchQuery,
            isRTL: true,
          ),
          
          SizedBox(height: 12.h),
          
          // Filter chips
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  label: 'status'.tr,
                  value: controller.selectedStatus,
                  options: controller.statusOptions,
                  onChanged: controller.updateStatusFilter,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildFilterChip(
                  label: 'type'.tr,
                  value: controller.selectedType,
                  options: controller.typeOptions,
                  onChanged: controller.updateTypeFilter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required RxString value,
    required List<String> options,
    required Function(String) onChanged,
  }) {
    return Obx(() => Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value.value,
          isExpanded: true,
          hint: Text(label),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(
                option == 'all' ? 'all'.tr : option.tr,
                style: AppTextStyles.bodySmall,
              ),
            );
          }).toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ),
    ));
  }

  Widget _buildCustomersList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (controller.filteredCustomers.isEmpty) {
        return _buildEmptyState();
      }

      return RefreshIndicator(
        onRefresh: controller.refreshCustomers,
        child: ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: controller.filteredCustomers.length,
          itemBuilder: (context, index) {
            final customer = controller.filteredCustomers[index];
            return CustomerCard(
              customer: customer,
              onTap: () => controller.navigateToCustomerDetails(customer.id),
              onEdit: () => controller.navigateToEditCustomer(customer.id),
              onDelete: () => controller.deleteCustomer(customer.id),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64.sp,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            'no_customers_found'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'try_adjusting_filters'.tr,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: controller.navigateToCreateCustomer,
            icon: const Icon(Icons.add),
            label: Text('create_customer'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: controller.navigateToCreateCustomer,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      child: const Icon(Icons.add),
    );
  }
}
