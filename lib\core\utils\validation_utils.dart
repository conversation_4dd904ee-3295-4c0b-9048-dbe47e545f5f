import 'package:get/get.dart';

/// Utility functions for form validation with Arabic error messages
class ValidationUtils {
  ValidationUtils._(); // Prevent instantiation

  /// Validate required field
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '${fieldName}_required'.tr 
          : 'field_required'.tr;
    }
    return null;
  }

  /// Validate email address
  static String? validateEmail(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'email_required'.tr;
    }
    
    if (!GetUtils.isEmail(value.trim())) {
      return 'invalid_email'.tr;
    }
    
    return null;
  }

  /// Validate phone number
  static String? validatePhone(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'phone_required'.tr;
    }
    
    // Remove spaces and special characters for validation
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Check if it's a valid Saudi phone number format
    if (!RegExp(r'^(05|5)[0-9]{8}$').hasMatch(cleanPhone) && 
        !RegExp(r'^\+966[5][0-9]{8}$').hasMatch(cleanPhone)) {
      return 'invalid_phone'.tr;
    }
    
    return null;
  }

  /// Validate password
  static String? validatePassword(String? value, {int minLength = 6}) {
    if (value == null || value.isEmpty) {
      return 'password_required'.tr;
    }
    
    if (value.length < minLength) {
      return 'password_min_length'.tr.replaceAll('#', minLength.toString());
    }
    
    return null;
  }

  /// Validate password confirmation
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'confirm_password_required'.tr;
    }
    
    if (value != password) {
      return 'passwords_do_not_match'.tr;
    }
    
    return null;
  }

  /// Validate name (Arabic and English support)
  static String? validateName(String? value, {int minLength = 2}) {
    if (value == null || value.trim().isEmpty) {
      return 'name_required'.tr;
    }
    
    if (value.trim().length < minLength) {
      return 'name_min_length'.tr.replaceAll('#', minLength.toString());
    }
    
    // Check if name contains only letters, spaces, and Arabic characters
    if (!RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$').hasMatch(value.trim())) {
      return 'invalid_name_format'.tr;
    }
    
    return null;
  }

  /// Validate numeric value
  static String? validateNumeric(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'numeric_value_required'.tr;
    }
    
    if (double.tryParse(value.trim()) == null) {
      return 'invalid_numeric_value'.tr;
    }
    
    return null;
  }

  /// Validate positive number
  static String? validatePositiveNumber(String? value, {bool isRequired = true}) {
    final numericValidation = validateNumeric(value, isRequired: isRequired);
    if (numericValidation != null) {
      return numericValidation;
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final number = double.tryParse(value.trim());
      if (number != null && number <= 0) {
        return 'value_must_be_positive'.tr;
      }
    }
    
    return null;
  }

  /// Validate amount (currency)
  static String? validateAmount(String? value, {bool isRequired = true, double? minAmount, double? maxAmount}) {
    final positiveValidation = validatePositiveNumber(value, isRequired: isRequired);
    if (positiveValidation != null) {
      return positiveValidation;
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final amount = double.tryParse(value.trim());
      if (amount != null) {
        if (minAmount != null && amount < minAmount) {
          return 'amount_too_small'.tr.replaceAll('#', minAmount.toString());
        }
        if (maxAmount != null && amount > maxAmount) {
          return 'amount_too_large'.tr.replaceAll('#', maxAmount.toString());
        }
      }
    }
    
    return null;
  }

  /// Validate quantity
  static String? validateQuantity(String? value, {bool isRequired = true, int? minQuantity, int? maxQuantity}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'quantity_required'.tr;
    }
    
    final quantity = int.tryParse(value.trim());
    if (quantity == null) {
      return 'invalid_quantity'.tr;
    }
    
    if (quantity <= 0) {
      return 'quantity_must_be_positive'.tr;
    }
    
    if (minQuantity != null && quantity < minQuantity) {
      return 'quantity_too_small'.tr.replaceAll('#', minQuantity.toString());
    }
    
    if (maxQuantity != null && quantity > maxQuantity) {
      return 'quantity_too_large'.tr.replaceAll('#', maxQuantity.toString());
    }
    
    return null;
  }

  /// Validate text length
  static String? validateLength(String? value, {int? minLength, int? maxLength, bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'field_required'.tr;
    }
    
    final length = value.trim().length;
    
    if (minLength != null && length < minLength) {
      return 'text_too_short'.tr.replaceAll('#', minLength.toString());
    }
    
    if (maxLength != null && length > maxLength) {
      return 'text_too_long'.tr.replaceAll('#', maxLength.toString());
    }
    
    return null;
  }

  /// Validate date
  static String? validateDate(DateTime? value, {bool isRequired = true}) {
    if (!isRequired && value == null) {
      return null;
    }
    
    if (value == null) {
      return 'date_required'.tr;
    }
    
    return null;
  }

  /// Validate future date
  static String? validateFutureDate(DateTime? value, {bool isRequired = true}) {
    final dateValidation = validateDate(value, isRequired: isRequired);
    if (dateValidation != null) {
      return dateValidation;
    }
    
    if (value != null && value.isBefore(DateTime.now())) {
      return 'date_must_be_future'.tr;
    }
    
    return null;
  }

  /// Validate past date
  static String? validatePastDate(DateTime? value, {bool isRequired = true}) {
    final dateValidation = validateDate(value, isRequired: isRequired);
    if (dateValidation != null) {
      return dateValidation;
    }
    
    if (value != null && value.isAfter(DateTime.now())) {
      return 'date_must_be_past'.tr;
    }
    
    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'url_required'.tr;
    }
    
    if (!GetUtils.isURL(value.trim())) {
      return 'invalid_url'.tr;
    }
    
    return null;
  }

  /// Validate dropdown selection
  static String? validateDropdown(dynamic value, {bool isRequired = true}) {
    if (!isRequired && value == null) {
      return null;
    }
    
    if (value == null) {
      return 'selection_required'.tr;
    }
    
    return null;
  }

  /// Validate checkbox
  static String? validateCheckbox(bool? value, {bool mustBeTrue = true}) {
    if (mustBeTrue && (value == null || !value)) {
      return 'checkbox_required'.tr;
    }
    
    return null;
  }

  /// Validate credit card number (basic validation)
  static String? validateCreditCard(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'credit_card_required'.tr;
    }
    
    // Remove spaces and dashes
    final cleanCard = value.replaceAll(RegExp(r'[\s\-]'), '');
    
    // Check if it's numeric and has valid length
    if (!RegExp(r'^\d{13,19}$').hasMatch(cleanCard)) {
      return 'invalid_credit_card'.tr;
    }
    
    return null;
  }

  /// Validate Saudi national ID
  static String? validateSaudiNationalId(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'national_id_required'.tr;
    }
    
    // Saudi national ID should be 10 digits starting with 1 or 2
    if (!RegExp(r'^[12]\d{9}$').hasMatch(value.trim())) {
      return 'invalid_national_id'.tr;
    }
    
    return null;
  }

  /// Validate commercial registration number
  static String? validateCommercialRegistration(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'commercial_registration_required'.tr;
    }
    
    // Commercial registration should be 10 digits
    if (!RegExp(r'^\d{10}$').hasMatch(value.trim())) {
      return 'invalid_commercial_registration'.tr;
    }
    
    return null;
  }

  /// Validate VAT number
  static String? validateVatNumber(String? value, {bool isRequired = true}) {
    if (!isRequired && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return 'vat_number_required'.tr;
    }
    
    // Saudi VAT number should be 15 digits
    if (!RegExp(r'^\d{15}$').hasMatch(value.trim())) {
      return 'invalid_vat_number'.tr;
    }
    
    return null;
  }

  /// Combine multiple validators
  static String? Function(String?) combineValidators(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
