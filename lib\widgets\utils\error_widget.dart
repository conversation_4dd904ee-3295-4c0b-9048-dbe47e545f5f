import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../forms/custom_button.dart';

/// Error widget for displaying error states
class AppErrorWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final IconData? icon;
  final String? actionText;
  final VoidCallback? onAction;
  final String? secondaryActionText;
  final VoidCallback? onSecondaryAction;
  final bool showAction;
  final EdgeInsetsGeometry? padding;
  final ErrorType type;
  final dynamic error;

  const AppErrorWidget({
    super.key,
    this.title,
    this.message,
    this.icon,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showAction = true,
    this.padding,
    this.type = ErrorType.general,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIcon(),
          SizedBox(height: 24.h),
          _buildTitle(),
          SizedBox(height: 12.h),
          _buildMessage(),
          if (showAction) ...[
            SizedBox(height: 32.h),
            _buildActions(),
          ],
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        borderRadius: BorderRadius.circular(40.r),
      ),
      child: Icon(
        icon ?? _getDefaultIcon(),
        size: 40.w,
        color: _getIconColor(),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      (title ?? _getDefaultTitle()).tr,
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.onSurface,
        fontWeight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildMessage() {
    return Text(
      (message ?? _getDefaultMessage()).tr,
      style: AppTextStyles.bodyLarge.copyWith(
        color: AppColors.onSurfaceVariant,
      ),
      textAlign: TextAlign.center,
      maxLines: 4,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildActions() {
    return Column(
      children: [
        if (actionText != null && onAction != null)
          CustomButton(
            text: actionText!,
            onPressed: onAction,
            type: ButtonType.primary,
            size: ButtonSize.medium,
            isFullWidth: true,
          ),
        if (secondaryActionText != null && onSecondaryAction != null) ...[
          SizedBox(height: 12.h),
          CustomButton(
            text: secondaryActionText!,
            onPressed: onSecondaryAction,
            type: ButtonType.outline,
            size: ButtonSize.medium,
            isFullWidth: true,
          ),
        ],
      ],
    );
  }

  Color _getIconBackgroundColor() {
    switch (type) {
      case ErrorType.general:
      case ErrorType.validation:
        return AppColors.error.withValues(alpha: 0.1);
      case ErrorType.network:
        return AppColors.primary.withValues(alpha: 0.1);
      case ErrorType.server:
        return AppColors.error.withValues(alpha: 0.1);
      case ErrorType.notFound:
        return AppColors.onSurfaceVariant.withValues(alpha: 0.1);
      case ErrorType.permission:
        return AppColors.error.withValues(alpha: 0.1);
    }
  }

  Color _getIconColor() {
    switch (type) {
      case ErrorType.general:
      case ErrorType.validation:
      case ErrorType.server:
      case ErrorType.permission:
        return AppColors.error;
      case ErrorType.network:
        return AppColors.primary;
      case ErrorType.notFound:
        return AppColors.onSurfaceVariant;
    }
  }

  IconData _getDefaultIcon() {
    switch (type) {
      case ErrorType.general:
        return Icons.error_outline;
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.validation:
        return Icons.warning_outlined;
      case ErrorType.permission:
        return Icons.lock_outline;
    }
  }

  String _getDefaultTitle() {
    switch (type) {
      case ErrorType.general:
        return 'error_occurred';
      case ErrorType.network:
        return 'network_error';
      case ErrorType.server:
        return 'server_error';
      case ErrorType.notFound:
        return 'not_found';
      case ErrorType.validation:
        return 'validation_error';
      case ErrorType.permission:
        return 'permission_denied';
    }
  }

  String _getDefaultMessage() {
    switch (type) {
      case ErrorType.general:
        return 'something_went_wrong';
      case ErrorType.network:
        return 'check_internet_connection';
      case ErrorType.server:
        return 'server_temporarily_unavailable';
      case ErrorType.notFound:
        return 'requested_resource_not_found';
      case ErrorType.validation:
        return 'please_check_your_input';
      case ErrorType.permission:
        return 'insufficient_permissions';
    }
  }
}

/// Error types for different contexts
enum ErrorType {
  general,
  network,
  server,
  notFound,
  validation,
  permission,
}

/// Predefined error widgets for common scenarios
class AppErrorWidgets {
  static AppErrorWidget get networkError => AppErrorWidget(
    type: ErrorType.network,
    actionText: 'retry',
    onAction: () {
      // Implement retry logic
    },
  );

  static AppErrorWidget get serverError => AppErrorWidget(
    type: ErrorType.server,
    actionText: 'retry',
    secondaryActionText: 'contact_support',
    onAction: () {
      // Implement retry logic
    },
    onSecondaryAction: () {
      // Implement contact support logic
    },
  );

  static AppErrorWidget get notFoundError => AppErrorWidget(
    type: ErrorType.notFound,
    actionText: 'go_back',
    onAction: () => Get.back(),
  );

  static AppErrorWidget get permissionError => AppErrorWidget(
    type: ErrorType.permission,
    actionText: 'request_permission',
    onAction: () {
      // Implement permission request logic
    },
  );

  static AppErrorWidget validationError(String message) => AppErrorWidget(
    type: ErrorType.validation,
    message: message,
    showAction: false,
  );
}

/// Compact error widget for smaller spaces
class CompactErrorWidget extends StatelessWidget {
  final String message;
  final IconData? icon;
  final VoidCallback? onRetry;
  final Color? backgroundColor;

  const CompactErrorWidget({
    super.key,
    required this.message,
    this.icon,
    this.onRetry,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon ?? Icons.error_outline,
            size: 20.w,
            color: AppColors.error,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              message.tr,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
          if (onRetry != null) ...[
            SizedBox(width: 8.w),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(
                Icons.refresh,
                color: AppColors.error,
              ),
              iconSize: 20.w,
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(
                minWidth: 24.w,
                minHeight: 24.w,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Error boundary widget for catching and displaying errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      
      return AppErrorWidget(
        type: ErrorType.general,
        message: _error.toString(),
        actionText: 'retry',
        onAction: () {
          setState(() {
            _error = null;
            _stackTrace = null;
          });
        },
      );
    }

    return widget.child;
  }


}
