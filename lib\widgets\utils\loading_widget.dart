import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';

/// Loading widget types
enum LoadingType {
  circular,
  linear,
  dots,
  shimmer,
  skeleton,
}

/// Loading widget following Al Ameen design system
class LoadingWidget extends StatelessWidget {
  final LoadingType type;
  final String? message;
  final Color? color;
  final double? size;
  final bool showMessage;
  final EdgeInsetsGeometry? padding;

  const LoadingWidget({
    super.key,
    this.type = LoadingType.circular,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildLoadingIndicator(),
          if (showMessage && message != null) ...[
            SizedBox(height: 16.h),
            Text(
              message!.tr,
              style: AppTextStyles.bodyMedium.copyWith(
                color: color ?? AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    switch (type) {
      case LoadingType.circular:
        return _buildCircularIndicator();
      case LoadingType.linear:
        return _buildLinearIndicator();
      case LoadingType.dots:
        return _buildDotsIndicator();
      case LoadingType.shimmer:
        return _buildShimmerIndicator();
      case LoadingType.skeleton:
        return _buildSkeletonIndicator();
    }
  }

  Widget _buildCircularIndicator() {
    return SizedBox(
      width: size ?? 40.w,
      height: size ?? 40.w,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildLinearIndicator() {
    return SizedBox(
      width: size ?? 200.w,
      child: LinearProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.primary,
        ),
        backgroundColor: AppColors.surfaceVariant,
      ),
    );
  }

  Widget _buildDotsIndicator() {
    return SizedBox(
      width: size ?? 60.w,
      height: 20.h,
      child: const DotsLoadingIndicator(),
    );
  }

  Widget _buildShimmerIndicator() {
    return SizedBox(
      width: size ?? 200.w,
      height: 20.h,
      child: const ShimmerLoadingIndicator(),
    );
  }

  Widget _buildSkeletonIndicator() {
    return SizedBox(
      width: size ?? 200.w,
      child: const SkeletonLoadingIndicator(),
    );
  }
}

/// Dots loading indicator
class DotsLoadingIndicator extends StatefulWidget {
  final Color? color;
  final double? size;

  const DotsLoadingIndicator({
    super.key,
    this.color,
    this.size,
  });

  @override
  State<DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _animations = List.generate(3, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.2,
            0.6 + index * 0.2,
            curve: Curves.easeInOut,
          ),
        ),
      );
    });

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              width: widget.size ?? 8.w,
              height: widget.size ?? 8.w,
              decoration: BoxDecoration(
                color: (widget.color ?? AppColors.primary)
                    .withValues(alpha: _animations[index].value),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

/// Shimmer loading indicator
class ShimmerLoadingIndicator extends StatefulWidget {
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoadingIndicator({
    super.key,
    this.baseColor,
    this.highlightColor,
  });

  @override
  State<ShimmerLoadingIndicator> createState() => _ShimmerLoadingIndicatorState();
}

class _ShimmerLoadingIndicatorState extends State<ShimmerLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                _animation.value - 1,
                _animation.value,
                _animation.value + 1,
              ],
              colors: [
                widget.baseColor ?? AppColors.surfaceVariant,
                widget.highlightColor ?? AppColors.surface,
                widget.baseColor ?? AppColors.surfaceVariant,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Skeleton loading indicator for list items
class SkeletonLoadingIndicator extends StatelessWidget {
  final int itemCount;
  final double? itemHeight;

  const SkeletonLoadingIndicator({
    super.key,
    this.itemCount = 3,
    this.itemHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(itemCount, (index) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: Row(
            children: [
              // Avatar placeholder
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 12.w),
              // Content placeholder
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 16.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.surfaceVariant,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      height: 12.h,
                      width: 0.7.sw,
                      decoration: BoxDecoration(
                        color: AppColors.surfaceVariant,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}

/// Full screen loading overlay
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;
  final Color? backgroundColor;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? AppColors.shadowMedium,
            child: Center(
              child: Container(
                padding: EdgeInsets.all(24.w),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowMedium,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: LoadingWidget(
                  message: message ?? 'loading'.tr,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
