import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/return.dart';

/// Return card widget for displaying return information
class ReturnCard extends StatelessWidget {
  final Return returnItem;
  final VoidCallback? onTap;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final VoidCallback? onProcess;
  final VoidCallback? onCancel;
  final VoidCallback? onEdit;

  const ReturnCard({
    super.key,
    required this.returnItem,
    this.onTap,
    this.onApprove,
    this.onReject,
    this.onProcess,
    this.onCancel,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            returnItem.formattedReturnNumber,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            returnItem.customerName,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Status chip
                    _buildStatusChip(),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    _buildActionsMenu(),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Return details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.receipt,
                        label: 'invoice_id'.tr,
                        value: returnItem.invoiceId ?? 'N/A',
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.calendar_today,
                        label: 'request_date'.tr,
                        value: FormatUtils.formatDate(returnItem.requestDate),
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 8.h),
                
                // Reason and amount
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.info,
                        label: 'reason'.tr,
                        value: _getReasonDisplayName(returnItem.reason),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.attach_money,
                        label: 'amount'.tr,
                        value: FormatUtils.formatCurrency(returnItem.totalAmount),
                      ),
                    ),
                  ],
                ),
                
                if (returnItem.items.isNotEmpty) ...[
                  SizedBox(height: 8.h),
                  _buildDetailItem(
                    icon: Icons.inventory,
                    label: 'items_count'.tr,
                    value: '${returnItem.items.length} ${returnItem.items.length == 1 ? 'item'.tr : 'items'.tr}',
                  ),
                ],
                
                if (returnItem.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 8.h),
                  _buildDetailItem(
                    icon: Icons.note,
                    label: 'notes'.tr,
                    value: returnItem.notes!,
                  ),
                ],
                
                // Rejection reason for rejected returns
                if (returnItem.isRejected && returnItem.rejectionReason?.isNotEmpty == true) ...[
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.cancel,
                          color: AppColors.error,
                          size: 16.sp,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'rejection_reason'.tr,
                                style: AppTextStyles.labelSmall.copyWith(
                                  color: AppColors.error,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                returnItem.rejectionReason!,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.error,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                // Action buttons for pending returns
                if (_shouldShowActionButtons()) ...[
                  SizedBox(height: 12.h),
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (returnItem.status) {
      case 'requested':
        statusColor = AppColors.warning;
        statusText = 'requested'.tr;
        statusIcon = Icons.pending;
        break;
      case 'approved':
        statusColor = AppColors.success;
        statusText = 'approved'.tr;
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = AppColors.error;
        statusText = 'rejected'.tr;
        statusIcon = Icons.cancel;
        break;
      case 'processing':
        statusColor = AppColors.info;
        statusText = 'processing'.tr;
        statusIcon = Icons.sync;
        break;
      case 'completed':
        statusColor = AppColors.success;
        statusText = 'completed'.tr;
        statusIcon = Icons.done_all;
        break;
      default:
        statusColor = AppColors.grey500;
        statusText = returnItem.status;
        statusIcon = Icons.help;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 14.sp,
            color: statusColor,
          ),
          SizedBox(width: 4.w),
          Text(
            statusText,
            style: AppTextStyles.labelSmall.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsMenu() {
    final actions = <PopupMenuItem<String>>[];

    if (returnItem.canBeApproved && onApprove != null) {
      actions.add(
        PopupMenuItem(
          value: 'approve',
          child: Row(
            children: [
              Icon(Icons.check, size: 18.sp, color: AppColors.success),
              SizedBox(width: 8.w),
              Text('approve'.tr),
            ],
          ),
        ),
      );
    }

    if (returnItem.canBeRejected && onReject != null) {
      actions.add(
        PopupMenuItem(
          value: 'reject',
          child: Row(
            children: [
              Icon(Icons.close, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text('reject'.tr),
            ],
          ),
        ),
      );
    }

    if (returnItem.canBeProcessed && onProcess != null) {
      actions.add(
        PopupMenuItem(
          value: 'process',
          child: Row(
            children: [
              Icon(Icons.sync, size: 18.sp, color: AppColors.info),
              SizedBox(width: 8.w),
              Text('process'.tr),
            ],
          ),
        ),
      );
    }

    if (onEdit != null && returnItem.isRequested) {
      actions.add(
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18.sp),
              SizedBox(width: 8.w),
              Text('edit'.tr),
            ],
          ),
        ),
      );
    }

    if (returnItem.canBeCancelled && onCancel != null) {
      actions.add(
        PopupMenuItem(
          value: 'cancel',
          child: Row(
            children: [
              Icon(Icons.cancel, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text('cancel'.tr),
            ],
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'approve':
            onApprove?.call();
            break;
          case 'reject':
            onReject?.call();
            break;
          case 'process':
            onProcess?.call();
            break;
          case 'edit':
            onEdit?.call();
            break;
          case 'cancel':
            onCancel?.call();
            break;
        }
      },
      itemBuilder: (context) => actions,
      child: Icon(
        Icons.more_vert,
        color: AppColors.onSurfaceVariant,
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: AppColors.onSurfaceVariant,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _shouldShowActionButtons() {
    return returnItem.canBeApproved || returnItem.canBeRejected;
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (returnItem.canBeApproved && onApprove != null) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onApprove,
              icon: Icon(Icons.check, size: 18.sp),
              label: Text('approve'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 8.h),
              ),
            ),
          ),
        ],
        if (returnItem.canBeRejected && onReject != null) ...[
          if (returnItem.canBeApproved) SizedBox(width: 8.w),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onReject,
              icon: Icon(Icons.close, size: 18.sp),
              label: Text('reject'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 8.h),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _getReasonDisplayName(String reason) {
    switch (reason) {
      case 'defective':
        return 'defective'.tr;
      case 'wrong_item':
        return 'wrong_item'.tr;
      case 'damaged':
        return 'damaged'.tr;
      case 'not_needed':
        return 'not_needed'.tr;
      case 'quality_issue':
        return 'quality_issue'.tr;
      case 'size_issue':
        return 'size_issue'.tr;
      case 'color_issue':
        return 'color_issue'.tr;
      case 'other':
        return 'other'.tr;
      default:
        return reason;
    }
  }
}
