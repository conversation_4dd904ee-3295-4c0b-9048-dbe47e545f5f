import 'package:json_annotation/json_annotation.dart';

part 'visit.g.dart';

/// Visit model for Al Ameen Sales App
@JsonSerializable()
class Visit {
  final String id;
  final String visitNumber;
  final String customerId;
  final String customerName;
  final String status; // 'scheduled', 'in_progress', 'completed', 'cancelled'
  final String purpose; // 'sales_call', 'delivery', 'collection', 'support', 'follow_up'
  final DateTime scheduledDate;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? notes;
  final String? outcome; // 'successful', 'no_contact', 'rescheduled', 'cancelled'
  final List<String>? attachments;
  final VisitLocation? location;
  final String? nextAction;
  final DateTime? nextVisitDate;
  final String assignedTo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? cancelReason;
  final DateTime? cancelledAt;

  const Visit({
    required this.id,
    required this.visitNumber,
    required this.customerId,
    required this.customerName,
    required this.status,
    required this.purpose,
    required this.scheduledDate,
    this.startTime,
    this.endTime,
    this.notes,
    this.outcome,
    this.attachments,
    this.location,
    this.nextAction,
    this.nextVisitDate,
    required this.assignedTo,
    required this.createdAt,
    required this.updatedAt,
    this.cancelReason,
    this.cancelledAt,
  });

  /// Get formatted visit number
  String get formattedVisitNumber => 'VIS-$visitNumber';

  /// Check if visit is scheduled
  bool get isScheduled => status == 'scheduled';

  /// Check if visit is in progress
  bool get isInProgress => status == 'in_progress';

  /// Check if visit is completed
  bool get isCompleted => status == 'completed';

  /// Check if visit is cancelled
  bool get isCancelled => status == 'cancelled';

  /// Check if visit can be started
  bool get canBeStarted => isScheduled && DateTime.now().isAfter(scheduledDate.subtract(const Duration(minutes: 15)));

  /// Check if visit can be completed
  bool get canBeCompleted => isInProgress;

  /// Check if visit can be cancelled
  bool get canBeCancelled => isScheduled || isInProgress;

  /// Get visit duration
  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }

  /// Get formatted duration
  String get formattedDuration {
    final dur = duration;
    if (dur == null) return 'N/A';
    
    final hours = dur.inHours;
    final minutes = dur.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Check if visit is overdue
  bool get isOverdue => isScheduled && DateTime.now().isAfter(scheduledDate.add(const Duration(hours: 1)));

  /// Check if visit is today
  bool get isToday {
    final now = DateTime.now();
    final visitDate = scheduledDate;
    return now.year == visitDate.year && 
           now.month == visitDate.month && 
           now.day == visitDate.day;
  }

  factory Visit.fromJson(Map<String, dynamic> json) => _$VisitFromJson(json);
  Map<String, dynamic> toJson() => _$VisitToJson(this);

  Visit copyWith({
    String? id,
    String? visitNumber,
    String? customerId,
    String? customerName,
    String? status,
    String? purpose,
    DateTime? scheduledDate,
    DateTime? startTime,
    DateTime? endTime,
    String? notes,
    String? outcome,
    List<String>? attachments,
    VisitLocation? location,
    String? nextAction,
    DateTime? nextVisitDate,
    String? assignedTo,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cancelReason,
    DateTime? cancelledAt,
  }) {
    return Visit(
      id: id ?? this.id,
      visitNumber: visitNumber ?? this.visitNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      status: status ?? this.status,
      purpose: purpose ?? this.purpose,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      notes: notes ?? this.notes,
      outcome: outcome ?? this.outcome,
      attachments: attachments ?? this.attachments,
      location: location ?? this.location,
      nextAction: nextAction ?? this.nextAction,
      nextVisitDate: nextVisitDate ?? this.nextVisitDate,
      assignedTo: assignedTo ?? this.assignedTo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancelReason: cancelReason ?? this.cancelReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Visit && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Visit(id: $id, number: $visitNumber, customer: $customerName, status: $status)';
}

/// Visit location information
@JsonSerializable()
class VisitLocation {
  final String? address;
  final double? latitude;
  final double? longitude;
  final DateTime? checkedInAt;
  final DateTime? checkedOutAt;
  final double? accuracy;

  const VisitLocation({
    this.address,
    this.latitude,
    this.longitude,
    this.checkedInAt,
    this.checkedOutAt,
    this.accuracy,
  });

  /// Check if location has coordinates
  bool get hasCoordinates => latitude != null && longitude != null;

  /// Check if checked in
  bool get isCheckedIn => checkedInAt != null && checkedOutAt == null;

  /// Check if checked out
  bool get isCheckedOut => checkedInAt != null && checkedOutAt != null;

  /// Get visit duration at location
  Duration? get visitDuration {
    if (checkedInAt != null && checkedOutAt != null) {
      return checkedOutAt!.difference(checkedInAt!);
    }
    return null;
  }

  factory VisitLocation.fromJson(Map<String, dynamic> json) => _$VisitLocationFromJson(json);
  Map<String, dynamic> toJson() => _$VisitLocationToJson(this);

  VisitLocation copyWith({
    String? address,
    double? latitude,
    double? longitude,
    DateTime? checkedInAt,
    DateTime? checkedOutAt,
    double? accuracy,
  }) {
    return VisitLocation(
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      checkedInAt: checkedInAt ?? this.checkedInAt,
      checkedOutAt: checkedOutAt ?? this.checkedOutAt,
      accuracy: accuracy ?? this.accuracy,
    );
  }
}

/// Visit filters for searching and filtering
class VisitFilters {
  final String? searchQuery;
  final List<String>? statuses;
  final List<String>? purposes;
  final List<String>? outcomes;
  final String? customerId;
  final String? assignedTo;
  final DateTime? scheduledDateFrom;
  final DateTime? scheduledDateTo;
  final DateTime? createdDateFrom;
  final DateTime? createdDateTo;
  final bool? isOverdue;
  final bool? isToday;
  final String? sortBy; // 'scheduledDate', 'customerName', 'status', 'createdAt'
  final String? sortOrder; // 'asc', 'desc'

  const VisitFilters({
    this.searchQuery,
    this.statuses,
    this.purposes,
    this.outcomes,
    this.customerId,
    this.assignedTo,
    this.scheduledDateFrom,
    this.scheduledDateTo,
    this.createdDateFrom,
    this.createdDateTo,
    this.isOverdue,
    this.isToday,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery?.isNotEmpty == true) params['search'] = searchQuery;
    if (statuses?.isNotEmpty == true) params['statuses'] = statuses!.join(',');
    if (purposes?.isNotEmpty == true) params['purposes'] = purposes!.join(',');
    if (outcomes?.isNotEmpty == true) params['outcomes'] = outcomes!.join(',');
    if (customerId?.isNotEmpty == true) params['customer_id'] = customerId;
    if (assignedTo?.isNotEmpty == true) params['assigned_to'] = assignedTo;
    if (scheduledDateFrom != null) params['scheduled_date_from'] = scheduledDateFrom!.toIso8601String();
    if (scheduledDateTo != null) params['scheduled_date_to'] = scheduledDateTo!.toIso8601String();
    if (createdDateFrom != null) params['created_date_from'] = createdDateFrom!.toIso8601String();
    if (createdDateTo != null) params['created_date_to'] = createdDateTo!.toIso8601String();
    if (isOverdue != null) params['is_overdue'] = isOverdue;
    if (isToday != null) params['is_today'] = isToday;
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}

/// Visit purposes enum
class VisitPurposes {
  static const String salesCall = 'sales_call';
  static const String delivery = 'delivery';
  static const String collection = 'collection';
  static const String support = 'support';
  static const String followUp = 'follow_up';
  static const String maintenance = 'maintenance';
  static const String training = 'training';
  static const String survey = 'survey';

  static const List<String> all = [
    salesCall,
    delivery,
    collection,
    support,
    followUp,
    maintenance,
    training,
    survey,
  ];

  static String getDisplayName(String purpose) {
    switch (purpose) {
      case salesCall:
        return 'Sales Call';
      case delivery:
        return 'Delivery';
      case collection:
        return 'Collection';
      case support:
        return 'Support';
      case followUp:
        return 'Follow Up';
      case maintenance:
        return 'Maintenance';
      case training:
        return 'Training';
      case survey:
        return 'Survey';
      default:
        return purpose;
    }
  }
}
