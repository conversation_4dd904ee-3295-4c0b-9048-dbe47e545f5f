import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'invoices_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../core/utils/format_utils.dart';
import '../../../widgets/forms/custom_text_field.dart';
import '../../../widgets/cards/invoice_card.dart';

/// Invoices list screen for retail representatives
class InvoicesScreen extends GetView<InvoicesController> {
  const InvoicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            // Summary cards
            _buildSummaryCards(),
            
            // Search and filters
            _buildSearchAndFilters(),
            
            // Invoices list
            Expanded(
              child: _buildInvoicesList(),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('invoices'.tr),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: controller.clearFilters,
          icon: const Icon(Icons.clear_all),
          tooltip: 'clear_filters'.tr,
        ),
        IconButton(
          onPressed: controller.refreshInvoices,
          icon: const Icon(Icons.refresh),
          tooltip: 'refresh'.tr,
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() => Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              title: 'total_amount'.tr,
              value: FormatUtils.formatCurrency(controller.totalAmount),
              icon: Icons.account_balance_wallet,
              color: AppColors.primary,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildSummaryCard(
              title: 'paid_amount'.tr,
              value: FormatUtils.formatCurrency(controller.paidAmount),
              icon: Icons.payment,
              color: AppColors.success,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildSummaryCard(
              title: 'outstanding'.tr,
              value: FormatUtils.formatCurrency(controller.outstandingAmount),
              icon: Icons.pending_actions,
              color: AppColors.warning,
            ),
          ),
        ],
      )),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field
          CustomTextField(
            hintText: 'search_invoices'.tr,
            prefixIcon: Icons.search,
            onChanged: controller.updateSearchQuery,
            isRTL: true,
          ),
          
          SizedBox(height: 12.h),
          
          // Filter chips
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  label: 'status'.tr,
                  value: controller.selectedStatus,
                  options: controller.statusOptions,
                  onChanged: controller.updateStatusFilter,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildFilterChip(
                  label: 'date_range'.tr,
                  value: controller.selectedDateRange,
                  options: controller.dateRangeOptions,
                  onChanged: controller.updateDateRangeFilter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required RxString value,
    required List<String> options,
    required Function(String) onChanged,
  }) {
    return Obx(() => Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value.value,
          isExpanded: true,
          hint: Text(label),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(
                option == 'all' ? 'all'.tr : option.tr,
                style: AppTextStyles.bodySmall,
              ),
            );
          }).toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ),
    ));
  }

  Widget _buildInvoicesList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (controller.filteredInvoices.isEmpty) {
        return _buildEmptyState();
      }

      return RefreshIndicator(
        onRefresh: controller.refreshInvoices,
        child: ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: controller.filteredInvoices.length,
          itemBuilder: (context, index) {
            final invoice = controller.filteredInvoices[index];
            return InvoiceCard(
              invoice: invoice,
              onTap: () => controller.navigateToInvoiceDetails(invoice.id),
              onEdit: () => controller.navigateToEditInvoice(invoice.id),
              onSend: () => controller.markAsSent(invoice.id),
              onMarkPaid: () => _showMarkPaidDialog(invoice),
              onCancel: () => _showCancelInvoiceDialog(invoice.id),
              onPrint: () => controller.printInvoice(invoice.id),
              onShare: () => controller.shareInvoice(invoice.id),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64.sp,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            'no_invoices_found'.tr,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'try_adjusting_filters'.tr,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: controller.navigateToCreateInvoice,
            icon: const Icon(Icons.add),
            label: Text('create_invoice'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: controller.navigateToCreateInvoice,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      child: const Icon(Icons.add),
    );
  }

  void _showMarkPaidDialog(invoice) {
    final amountController = TextEditingController(
      text: invoice.totalAmount.toString(),
    );
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('mark_as_paid'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('enter_paid_amount'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: amountController,
                labelText: 'paid_amount'.tr,
                keyboardType: TextInputType.number,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('cancel'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                final amount = double.tryParse(amountController.text) ?? 0.0;
                if (amount > 0) {
                  Get.back();
                  controller.markAsPaid(invoice.id, amount);
                }
              },
              child: Text('confirm'.tr),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelInvoiceDialog(String invoiceId) {
    final reasonController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('cancel_invoice'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('cancel_invoice_reason'.tr),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: reasonController,
                hintText: 'enter_cancel_reason'.tr,
                maxLines: 2,
                isRTL: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('no'.tr),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                controller.cancelInvoice(invoiceId, reasonController.text.trim());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: Text('yes'.tr),
            ),
          ],
        ),
      ),
    );
  }
}
