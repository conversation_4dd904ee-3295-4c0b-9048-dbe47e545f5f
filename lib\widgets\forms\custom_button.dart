import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';

/// Button types for different use cases
enum ButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

/// Button sizes
enum ButtonSize {
  small,
  medium,
  large,
}

/// Custom button widget following Al Ameen design system
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;
  final bool iconOnRight;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.iconOnRight = false,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    final isEnabled = onPressed != null && !isLoading;
    
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: _getHeight(),
      child: _buildButton(isEnabled),
    );
  }

  Widget _buildButton(bool isEnabled) {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: _getElevatedButtonStyle(isEnabled),
          child: _buildButtonContent(),
        );
      case ButtonType.secondary:
        return ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: _getSecondaryButtonStyle(isEnabled),
          child: _buildButtonContent(),
        );
      case ButtonType.outline:
        return OutlinedButton(
          onPressed: isEnabled ? onPressed : null,
          style: _getOutlinedButtonStyle(isEnabled),
          child: _buildButtonContent(),
        );
      case ButtonType.text:
        return TextButton(
          onPressed: isEnabled ? onPressed : null,
          style: _getTextButtonStyle(isEnabled),
          child: _buildButtonContent(),
        );
      case ButtonType.danger:
        return ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: _getDangerButtonStyle(isEnabled),
          child: _buildButtonContent(),
        );
    }
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: _getLoadingSize(),
        height: _getLoadingSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getLoadingColor(),
          ),
        ),
      );
    }

    final textWidget = Text(
      text,
      style: _getTextStyle(),
      textAlign: TextAlign.center,
    );

    if (icon == null) {
      return textWidget;
    }

    final iconWidget = icon!;
    final spacing = SizedBox(width: 8.w);

    if (iconOnRight) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [textWidget, spacing, iconWidget],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [iconWidget, spacing, textWidget],
      );
    }
  }

  double _getHeight() {
    switch (size) {
      case ButtonSize.small:
        return 32.h;
      case ButtonSize.medium:
        return 40.h;
      case ButtonSize.large:
        return 48.h;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case ButtonSize.small:
        return 16.w;
      case ButtonSize.medium:
        return 20.w;
      case ButtonSize.large:
        return 24.w;
    }
  }

  Color _getLoadingColor() {
    switch (type) {
      case ButtonType.primary:
      case ButtonType.danger:
        return AppColors.onPrimary;
      case ButtonType.secondary:
        return AppColors.onSecondary;
      case ButtonType.outline:
      case ButtonType.text:
        return AppColors.primary;
    }
  }

  TextStyle _getTextStyle() {
    final baseStyle = switch (size) {
      ButtonSize.small => AppTextStyles.labelSmall,
      ButtonSize.medium => AppTextStyles.labelMedium,
      ButtonSize.large => AppTextStyles.labelLarge,
    };

    Color color = textColor ?? _getDefaultTextColor();
    
    return baseStyle.copyWith(
      color: color,
      fontWeight: FontWeight.w600,
    );
  }

  Color _getDefaultTextColor() {
    switch (type) {
      case ButtonType.primary:
      case ButtonType.danger:
        return AppColors.onPrimary;
      case ButtonType.secondary:
        return AppColors.onSecondary;
      case ButtonType.outline:
      case ButtonType.text:
        return AppColors.primary;
    }
  }

  ButtonStyle _getElevatedButtonStyle(bool isEnabled) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: textColor ?? AppColors.onPrimary,
      disabledBackgroundColor: AppColors.surfaceVariant,
      disabledForegroundColor: AppColors.onSurfaceVariant,
      elevation: 2,
      shadowColor: AppColors.shadowMedium,
      padding: padding ?? _getDefaultPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  ButtonStyle _getSecondaryButtonStyle(bool isEnabled) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppColors.secondary,
      foregroundColor: textColor ?? AppColors.onSecondary,
      disabledBackgroundColor: AppColors.surfaceVariant,
      disabledForegroundColor: AppColors.onSurfaceVariant,
      elevation: 1,
      shadowColor: AppColors.shadowLight,
      padding: padding ?? _getDefaultPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  ButtonStyle _getOutlinedButtonStyle(bool isEnabled) {
    return OutlinedButton.styleFrom(
      foregroundColor: textColor ?? AppColors.primary,
      disabledForegroundColor: AppColors.onSurfaceVariant,
      side: BorderSide(
        color: borderColor ?? AppColors.primary,
        width: 1,
      ),
      padding: padding ?? _getDefaultPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  ButtonStyle _getTextButtonStyle(bool isEnabled) {
    return TextButton.styleFrom(
      foregroundColor: textColor ?? AppColors.primary,
      disabledForegroundColor: AppColors.onSurfaceVariant,
      padding: padding ?? _getDefaultPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  ButtonStyle _getDangerButtonStyle(bool isEnabled) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppColors.error,
      foregroundColor: textColor ?? AppColors.onPrimary,
      disabledBackgroundColor: AppColors.surfaceVariant,
      disabledForegroundColor: AppColors.onSurfaceVariant,
      elevation: 2,
      shadowColor: AppColors.shadowMedium,
      padding: padding ?? _getDefaultPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  EdgeInsetsGeometry _getDefaultPadding() {
    switch (size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h);
      case ButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h);
      case ButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h);
    }
  }
}
