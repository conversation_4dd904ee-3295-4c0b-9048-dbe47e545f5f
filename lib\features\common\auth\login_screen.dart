import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'login_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';
import '../../../widgets/common/custom_button.dart';
import '../../../widgets/forms/custom_text_field.dart';

/// Login screen for Al Ameen Sales App
class LoginScreen extends GetView<LoginController> {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, // Force RTL for Arabic
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                SizedBox(height: 60.h),
                
                // Logo and title section
                _buildHeader(),
                
                SizedBox(height: 48.h),
                
                // Login form
                _buildLoginForm(),
                
                SizedBox(height: 24.h),
                
                // Login button
                _buildLoginButton(),
                
                SizedBox(height: 16.h),
                
                // Forgot password
                _buildForgotPassword(),
                
                SizedBox(height: 32.h),
                
                // Demo buttons (development only)
                _buildDemoButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App logo
        Container(
          width: 80.w,
          height: 80.w,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Icon(
            Icons.business_center,
            size: 40.sp,
            color: AppColors.onPrimary,
          ),
        ),
        
        SizedBox(height: 24.h),
        
        // App name
        Text(
          'app_arabic_name'.tr,
          style: AppTextStyles.displayMedium.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: 16.h),

        // Welcome text
        Text(
          'welcome_back'.tr,
          style: AppTextStyles.headlineMedium,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        // Email field
        CustomTextField(
          controller: controller.emailController,
          labelText: 'email'.tr,
          hintText: 'enter_email'.tr,
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icons.email_outlined,
          validator: controller.validateEmail,
          isRTL: true,
        ),

        SizedBox(height: 16.h),

        // Password field
        Obx(() => CustomTextField(
          controller: controller.passwordController,
          labelText: 'password'.tr,
          hintText: 'enter_password'.tr,
          obscureText: !controller.isPasswordVisible.value,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              controller.isPasswordVisible.value
                  ? Icons.visibility_off
                  : Icons.visibility,
            ),
            onPressed: controller.togglePasswordVisibility,
          ),
          validator: controller.validatePassword,
        )),
        
        SizedBox(height: 16.h),
        
        // Remember me checkbox
        Row(
          children: [
            Obx(() => Checkbox(
              value: controller.rememberMe.value,
              onChanged: controller.toggleRememberMe,
            )),
            Text(
              'remember_me'.tr,
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return Obx(() => CustomButton(
      text: 'login_button'.tr,
      onPressed: controller.isLoading.value ? null : controller.login,
      isLoading: controller.isLoading.value,
    ));
  }

  Widget _buildForgotPassword() {
    return TextButton(
      onPressed: controller.forgotPassword,
      child: Text(
        'forgot_password'.tr,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildDemoButtons() {
    return Column(
      children: [
        Text(
          'demo_login'.tr,
          style: AppTextStyles.labelMedium.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),

        SizedBox(height: 16.h),

        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: controller.quickLoginRetail,
                child: Text('retail_rep'.tr),
              ),
            ),

            SizedBox(width: 16.w),

            Expanded(
              child: OutlinedButton(
                onPressed: controller.quickLoginWholesale,
                child: Text('wholesale_rep'.tr),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
