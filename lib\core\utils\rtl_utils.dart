import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Utility functions for RTL (Right-to-Left) layout support
class RTLUtils {
  RTLUtils._(); // Prevent instantiation

  /// Check if current locale is RTL
  static bool get isRTL {
    final locale = Get.locale ?? const Locale('ar');
    return locale.languageCode == 'ar' || 
           locale.languageCode == 'he' || 
           locale.languageCode == 'fa' || 
           locale.languageCode == 'ur';
  }

  /// Get text direction based on current locale
  static TextDirection get textDirection {
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }

  /// Get text align based on current locale
  static TextAlign get textAlign {
    return isRTL ? TextAlign.right : TextAlign.left;
  }

  /// Get text align for center text
  static TextAlign get textAlignCenter => TextAlign.center;

  /// Get text align for opposite direction (useful for numbers)
  static TextAlign get textAlignOpposite {
    return isRTL ? TextAlign.left : TextAlign.right;
  }

  /// Wrap widget with Directionality for RTL support
  static Widget wrapWithDirectionality(Widget child) {
    return Directionality(
      textDirection: textDirection,
      child: child,
    );
  }

  /// Get edge insets with RTL support
  static EdgeInsets getEdgeInsets({
    double? start,
    double? end,
    double? top,
    double? bottom,
  }) {
    if (isRTL) {
      return EdgeInsets.only(
        left: end ?? 0,
        right: start ?? 0,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    } else {
      return EdgeInsets.only(
        left: start ?? 0,
        right: end ?? 0,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }
  }

  /// Get symmetric edge insets with RTL support
  static EdgeInsets getSymmetricEdgeInsets({
    double? horizontal,
    double? vertical,
  }) {
    return EdgeInsets.symmetric(
      horizontal: horizontal ?? 0,
      vertical: vertical ?? 0,
    );
  }

  /// Get border radius with RTL support
  static BorderRadius getBorderRadius({
    double? topStart,
    double? topEnd,
    double? bottomStart,
    double? bottomEnd,
  }) {
    if (isRTL) {
      return BorderRadius.only(
        topLeft: Radius.circular(topEnd ?? 0),
        topRight: Radius.circular(topStart ?? 0),
        bottomLeft: Radius.circular(bottomEnd ?? 0),
        bottomRight: Radius.circular(bottomStart ?? 0),
      );
    } else {
      return BorderRadius.only(
        topLeft: Radius.circular(topStart ?? 0),
        topRight: Radius.circular(topEnd ?? 0),
        bottomLeft: Radius.circular(bottomStart ?? 0),
        bottomRight: Radius.circular(bottomEnd ?? 0),
      );
    }
  }

  /// Get alignment with RTL support
  static Alignment getAlignment({
    required double x,
    required double y,
  }) {
    return Alignment(isRTL ? -x : x, y);
  }

  /// Get alignment for start (left in LTR, right in RTL)
  static Alignment get alignmentStart {
    return isRTL ? Alignment.centerRight : Alignment.centerLeft;
  }

  /// Get alignment for end (right in LTR, left in RTL)
  static Alignment get alignmentEnd {
    return isRTL ? Alignment.centerLeft : Alignment.centerRight;
  }

  /// Get cross axis alignment for start
  static CrossAxisAlignment get crossAxisAlignmentStart {
    return isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }

  /// Get cross axis alignment for end
  static CrossAxisAlignment get crossAxisAlignmentEnd {
    return isRTL ? CrossAxisAlignment.start : CrossAxisAlignment.end;
  }

  /// Get main axis alignment for start
  static MainAxisAlignment get mainAxisAlignmentStart {
    return isRTL ? MainAxisAlignment.end : MainAxisAlignment.start;
  }

  /// Get main axis alignment for end
  static MainAxisAlignment get mainAxisAlignmentEnd {
    return isRTL ? MainAxisAlignment.start : MainAxisAlignment.end;
  }

  /// Format number for RTL display (useful for Arabic numerals)
  static String formatNumber(num number, {bool useArabicNumerals = false}) {
    final numberString = number.toString();
    
    if (!useArabicNumerals) {
      return numberString;
    }

    // Convert to Arabic-Indic numerals
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const westernNumerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    String result = numberString;
    for (int i = 0; i < westernNumerals.length; i++) {
      result = result.replaceAll(westernNumerals[i], arabicNumerals[i]);
    }
    
    return result;
  }

  /// Get icon data with RTL support (flips icons if needed)
  static IconData getIconData(IconData icon, {bool shouldFlip = false}) {
    if (!shouldFlip || !isRTL) {
      return icon;
    }

    // Map of icons that should be flipped in RTL
    final flippedIcons = {
      Icons.arrow_forward: Icons.arrow_back,
      Icons.arrow_back: Icons.arrow_forward,
      Icons.arrow_forward_ios: Icons.arrow_back_ios,
      Icons.arrow_back_ios: Icons.arrow_forward_ios,
      Icons.chevron_right: Icons.chevron_left,
      Icons.chevron_left: Icons.chevron_right,
      Icons.keyboard_arrow_right: Icons.keyboard_arrow_left,
      Icons.keyboard_arrow_left: Icons.keyboard_arrow_right,
    };

    return flippedIcons[icon] ?? icon;
  }

  /// Get directional icon (alias for getIconData for compatibility)
  static IconData getDirectionalIcon(IconData icon) {
    return getIconData(icon, shouldFlip: true);
  }

  /// Get text style with RTL-appropriate font family
  static TextStyle getTextStyle(TextStyle baseStyle) {
    // Tajawal font is already set as default in theme
    // This method can be extended for additional RTL-specific styling
    return baseStyle;
  }

  /// Get locale-specific date format
  static String getDateFormat() {
    return isRTL ? 'dd/MM/yyyy' : 'MM/dd/yyyy';
  }

  /// Get locale-specific time format
  static String getTimeFormat() {
    return isRTL ? 'HH:mm' : 'h:mm a';
  }

  /// Get locale-specific currency format
  static String getCurrencyFormat() {
    return isRTL ? '# ر.س' : 'SAR #';
  }

  /// Check if text contains Arabic characters
  static bool containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  /// Get appropriate text direction for mixed content
  static TextDirection getTextDirectionForContent(String text) {
    if (containsArabic(text)) {
      return TextDirection.rtl;
    }
    return TextDirection.ltr;
  }

  /// Create a responsive row that adapts to RTL
  static Widget createResponsiveRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Row(
      textDirection: textDirection,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }

  /// Create a responsive column that adapts to RTL
  static Widget createResponsiveColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Column(
      textDirection: textDirection,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }

  /// Get safe padding for RTL layouts
  static EdgeInsets getSafePadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      left: isRTL ? mediaQuery.padding.right : mediaQuery.padding.left,
      right: isRTL ? mediaQuery.padding.left : mediaQuery.padding.right,
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
    );
  }

  /// Get app bar leading widget position
  static bool get automaticallyImplyLeading => !isRTL;

  /// Get floating action button location for RTL
  static FloatingActionButtonLocation get fabLocation {
    return isRTL 
        ? FloatingActionButtonLocation.startFloat 
        : FloatingActionButtonLocation.endFloat;
  }
}
