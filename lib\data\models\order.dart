import 'package:json_annotation/json_annotation.dart';

part 'order.g.dart';

/// Order model for Al Ameen Sales App
@JsonSerializable()
class Order {
  final String id;
  final String orderNumber;
  final String customerId;
  final String customerName;
  final String status; // 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final DateTime? shippedDate;
  final List<OrderItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double shippingAmount;
  final double totalAmount;
  final String paymentMethod; // 'cash', 'credit', 'check', 'transfer'
  final String paymentStatus; // 'pending', 'paid', 'partial', 'overdue'
  final String? notes;
  final String? deliveryAddress;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? cancelReason;
  final DateTime? cancelledAt;

  const Order({
    required this.id,
    required this.orderNumber,
    required this.customerId,
    required this.customerName,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    this.shippedDate,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.shippingAmount,
    required this.totalAmount,
    required this.paymentMethod,
    required this.paymentStatus,
    this.notes,
    this.deliveryAddress,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.cancelReason,
    this.cancelledAt,
  });

  /// Get formatted order number
  String get formattedOrderNumber => 'ORD-$orderNumber';

  /// Check if order is pending
  bool get isPending => status == 'pending';

  /// Check if order is confirmed
  bool get isConfirmed => status == 'confirmed';

  /// Check if order is cancelled
  bool get isCancelled => status == 'cancelled';

  /// Check if order is delivered
  bool get isDelivered => status == 'delivered';

  /// Check if order can be cancelled
  bool get canBeCancelled => isPending || isConfirmed;

  /// Check if order can be edited
  bool get canBeEdited => isPending;

  /// Get total quantity of items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  /// Get number of different products
  int get productCount => items.length;

  /// Check if payment is completed
  bool get isPaymentCompleted => paymentStatus == 'paid';

  /// Check if payment is overdue
  bool get isPaymentOverdue => paymentStatus == 'overdue';

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  Map<String, dynamic> toJson() => _$OrderToJson(this);

  Order copyWith({
    String? id,
    String? orderNumber,
    String? customerId,
    String? customerName,
    String? status,
    DateTime? orderDate,
    DateTime? deliveryDate,
    DateTime? shippedDate,
    List<OrderItem>? items,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? shippingAmount,
    double? totalAmount,
    String? paymentMethod,
    String? paymentStatus,
    String? notes,
    String? deliveryAddress,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cancelReason,
    DateTime? cancelledAt,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      status: status ?? this.status,
      orderDate: orderDate ?? this.orderDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      shippedDate: shippedDate ?? this.shippedDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      shippingAmount: shippingAmount ?? this.shippingAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      notes: notes ?? this.notes,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancelReason: cancelReason ?? this.cancelReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Order && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Order(id: $id, number: $orderNumber, customer: $customerName, status: $status)';
}

/// Order item model
@JsonSerializable()
class OrderItem {
  final String id;
  final String productId;
  final String productName;
  final String? productCode;
  final String? productImage;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final double? discountAmount;
  final double? taxAmount;
  final String? notes;
  final String unit; // 'pcs', 'kg', 'liter', etc.

  const OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productCode,
    this.productImage,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountAmount,
    this.taxAmount,
    this.notes,
    this.unit = 'pcs',
  });

  /// Get formatted product code
  String get formattedProductCode => productCode != null ? 'SKU-$productCode' : '';

  /// Get net price after discount
  double get netPrice => totalPrice - (discountAmount ?? 0);

  /// Get discount percentage
  double get discountPercentage {
    if (discountAmount == null || totalPrice == 0) return 0;
    return (discountAmount! / totalPrice) * 100;
  }

  factory OrderItem.fromJson(Map<String, dynamic> json) => _$OrderItemFromJson(json);
  Map<String, dynamic> toJson() => _$OrderItemToJson(this);

  OrderItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    String? productImage,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountAmount,
    double? taxAmount,
    String? notes,
    String? unit,
  }) {
    return OrderItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      productImage: productImage ?? this.productImage,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      notes: notes ?? this.notes,
      unit: unit ?? this.unit,
    );
  }

  @override
  String toString() => 'OrderItem(product: $productName, quantity: $quantity, price: $totalPrice)';
}

/// Order filters for searching and filtering
class OrderFilters {
  final String? searchQuery;
  final List<String>? statuses;
  final List<String>? paymentStatuses;
  final String? customerId;
  final DateTime? orderDateFrom;
  final DateTime? orderDateTo;
  final DateTime? deliveryDateFrom;
  final DateTime? deliveryDateTo;
  final double? minAmount;
  final double? maxAmount;
  final String? paymentMethod;
  final String? createdBy;
  final String? sortBy; // 'orderDate', 'totalAmount', 'customerName', 'status'
  final String? sortOrder; // 'asc', 'desc'

  const OrderFilters({
    this.searchQuery,
    this.statuses,
    this.paymentStatuses,
    this.customerId,
    this.orderDateFrom,
    this.orderDateTo,
    this.deliveryDateFrom,
    this.deliveryDateTo,
    this.minAmount,
    this.maxAmount,
    this.paymentMethod,
    this.createdBy,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery?.isNotEmpty == true) params['search'] = searchQuery;
    if (statuses?.isNotEmpty == true) params['statuses'] = statuses!.join(',');
    if (paymentStatuses?.isNotEmpty == true) params['payment_statuses'] = paymentStatuses!.join(',');
    if (customerId?.isNotEmpty == true) params['customer_id'] = customerId;
    if (orderDateFrom != null) params['order_date_from'] = orderDateFrom!.toIso8601String();
    if (orderDateTo != null) params['order_date_to'] = orderDateTo!.toIso8601String();
    if (deliveryDateFrom != null) params['delivery_date_from'] = deliveryDateFrom!.toIso8601String();
    if (deliveryDateTo != null) params['delivery_date_to'] = deliveryDateTo!.toIso8601String();
    if (minAmount != null) params['min_amount'] = minAmount;
    if (maxAmount != null) params['max_amount'] = maxAmount;
    if (paymentMethod?.isNotEmpty == true) params['payment_method'] = paymentMethod;
    if (createdBy?.isNotEmpty == true) params['created_by'] = createdBy;
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}
