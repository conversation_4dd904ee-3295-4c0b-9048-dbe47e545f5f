import 'package:json_annotation/json_annotation.dart';

part 'return.g.dart';

/// Return model for Al Ameen Sales App
@JsonSerializable()
class Return {
  final String id;
  final String returnNumber;
  final String orderId;
  final String? invoiceId;
  final String customerId;
  final String customerName;
  final String status; // 'requested', 'approved', 'rejected', 'processing', 'completed'
  final String reason; // 'defective', 'wrong_item', 'damaged', 'not_needed', 'other'
  final String? description;
  final DateTime requestDate;
  final DateTime? approvedDate;
  final DateTime? processedDate;
  final DateTime? completedDate;
  final List<ReturnItem> items;
  final double totalAmount;
  final double refundAmount;
  final String refundMethod; // 'cash', 'credit', 'store_credit', 'exchange'
  final String? notes;
  final List<String>? attachments;
  final String requestedBy;
  final String? approvedBy;
  final String? processedBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? rejectionReason;

  const Return({
    required this.id,
    required this.returnNumber,
    required this.orderId,
    this.invoiceId,
    required this.customerId,
    required this.customerName,
    required this.status,
    required this.reason,
    this.description,
    required this.requestDate,
    this.approvedDate,
    this.processedDate,
    this.completedDate,
    required this.items,
    required this.totalAmount,
    required this.refundAmount,
    required this.refundMethod,
    this.notes,
    this.attachments,
    required this.requestedBy,
    this.approvedBy,
    this.processedBy,
    required this.createdAt,
    required this.updatedAt,
    this.rejectionReason,
  });

  /// Get formatted return number
  String get formattedReturnNumber => 'RET-$returnNumber';

  /// Check if return is requested
  bool get isRequested => status == 'requested';

  /// Check if return is approved
  bool get isApproved => status == 'approved';

  /// Check if return is rejected
  bool get isRejected => status == 'rejected';

  /// Check if return is processing
  bool get isProcessing => status == 'processing';

  /// Check if return is completed
  bool get isCompleted => status == 'completed';

  /// Check if return can be approved
  bool get canBeApproved => isRequested;

  /// Check if return can be rejected
  bool get canBeRejected => isRequested;

  /// Check if return can be processed
  bool get canBeProcessed => isApproved;

  /// Check if return can be completed
  bool get canBeCompleted => isProcessing;

  /// Check if return can be cancelled
  bool get canBeCancelled => isRequested || isApproved;

  /// Get total quantity of items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  /// Get number of different products
  int get productCount => items.length;

  /// Get processing time
  Duration? get processingTime {
    if (completedDate != null) {
      return completedDate!.difference(requestDate);
    }
    return null;
  }

  /// Get refund percentage
  double get refundPercentage {
    if (totalAmount == 0) return 0;
    return (refundAmount / totalAmount) * 100;
  }

  factory Return.fromJson(Map<String, dynamic> json) => _$ReturnFromJson(json);
  Map<String, dynamic> toJson() => _$ReturnToJson(this);

  Return copyWith({
    String? id,
    String? returnNumber,
    String? orderId,
    String? invoiceId,
    String? customerId,
    String? customerName,
    String? status,
    String? reason,
    String? description,
    DateTime? requestDate,
    DateTime? approvedDate,
    DateTime? processedDate,
    DateTime? completedDate,
    List<ReturnItem>? items,
    double? totalAmount,
    double? refundAmount,
    String? refundMethod,
    String? notes,
    List<String>? attachments,
    String? requestedBy,
    String? approvedBy,
    String? processedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? rejectionReason,
  }) {
    return Return(
      id: id ?? this.id,
      returnNumber: returnNumber ?? this.returnNumber,
      orderId: orderId ?? this.orderId,
      invoiceId: invoiceId ?? this.invoiceId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      description: description ?? this.description,
      requestDate: requestDate ?? this.requestDate,
      approvedDate: approvedDate ?? this.approvedDate,
      processedDate: processedDate ?? this.processedDate,
      completedDate: completedDate ?? this.completedDate,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      refundAmount: refundAmount ?? this.refundAmount,
      refundMethod: refundMethod ?? this.refundMethod,
      notes: notes ?? this.notes,
      attachments: attachments ?? this.attachments,
      requestedBy: requestedBy ?? this.requestedBy,
      approvedBy: approvedBy ?? this.approvedBy,
      processedBy: processedBy ?? this.processedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Return && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Return(id: $id, number: $returnNumber, customer: $customerName, status: $status)';
}

/// Return item model
@JsonSerializable()
class ReturnItem {
  final String id;
  final String productId;
  final String productName;
  final String? productCode;
  final String? productImage;
  final int quantity;
  final int returnedQuantity;
  final double unitPrice;
  final double totalPrice;
  final double refundAmount;
  final String condition; // 'new', 'used', 'damaged', 'defective'
  final String? notes;
  final String unit;

  const ReturnItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productCode,
    this.productImage,
    required this.quantity,
    required this.returnedQuantity,
    required this.unitPrice,
    required this.totalPrice,
    required this.refundAmount,
    required this.condition,
    this.notes,
    this.unit = 'pcs',
  });

  /// Get refund percentage for this item
  double get refundPercentage {
    if (totalPrice == 0) return 0;
    return (refundAmount / totalPrice) * 100;
  }

  /// Check if partial return
  bool get isPartialReturn => returnedQuantity < quantity;

  /// Check if full return
  bool get isFullReturn => returnedQuantity >= quantity;

  factory ReturnItem.fromJson(Map<String, dynamic> json) => _$ReturnItemFromJson(json);
  Map<String, dynamic> toJson() => _$ReturnItemToJson(this);

  ReturnItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    String? productImage,
    int? quantity,
    int? returnedQuantity,
    double? unitPrice,
    double? totalPrice,
    double? refundAmount,
    String? condition,
    String? notes,
    String? unit,
  }) {
    return ReturnItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      productImage: productImage ?? this.productImage,
      quantity: quantity ?? this.quantity,
      returnedQuantity: returnedQuantity ?? this.returnedQuantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      refundAmount: refundAmount ?? this.refundAmount,
      condition: condition ?? this.condition,
      notes: notes ?? this.notes,
      unit: unit ?? this.unit,
    );
  }
}

/// Return filters for searching and filtering
class ReturnFilters {
  final String? searchQuery;
  final List<String>? statuses;
  final List<String>? reasons;
  final String? customerId;
  final String? orderId;
  final String? invoiceId;
  final DateTime? requestDateFrom;
  final DateTime? requestDateTo;
  final DateTime? completedDateFrom;
  final DateTime? completedDateTo;
  final double? minAmount;
  final double? maxAmount;
  final String? requestedBy;
  final String? approvedBy;
  final String? sortBy; // 'requestDate', 'totalAmount', 'customerName', 'status'
  final String? sortOrder; // 'asc', 'desc'

  const ReturnFilters({
    this.searchQuery,
    this.statuses,
    this.reasons,
    this.customerId,
    this.orderId,
    this.invoiceId,
    this.requestDateFrom,
    this.requestDateTo,
    this.completedDateFrom,
    this.completedDateTo,
    this.minAmount,
    this.maxAmount,
    this.requestedBy,
    this.approvedBy,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (searchQuery?.isNotEmpty == true) params['search'] = searchQuery;
    if (statuses?.isNotEmpty == true) params['statuses'] = statuses!.join(',');
    if (reasons?.isNotEmpty == true) params['reasons'] = reasons!.join(',');
    if (customerId?.isNotEmpty == true) params['customer_id'] = customerId;
    if (orderId?.isNotEmpty == true) params['order_id'] = orderId;
    if (invoiceId?.isNotEmpty == true) params['invoice_id'] = invoiceId;
    if (requestDateFrom != null) params['request_date_from'] = requestDateFrom!.toIso8601String();
    if (requestDateTo != null) params['request_date_to'] = requestDateTo!.toIso8601String();
    if (completedDateFrom != null) params['completed_date_from'] = completedDateFrom!.toIso8601String();
    if (completedDateTo != null) params['completed_date_to'] = completedDateTo!.toIso8601String();
    if (minAmount != null) params['min_amount'] = minAmount;
    if (maxAmount != null) params['max_amount'] = maxAmount;
    if (requestedBy?.isNotEmpty == true) params['requested_by'] = requestedBy;
    if (approvedBy?.isNotEmpty == true) params['approved_by'] = approvedBy;
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}

/// Return reasons enum
class ReturnReasons {
  static const String defective = 'defective';
  static const String wrongItem = 'wrong_item';
  static const String damaged = 'damaged';
  static const String notNeeded = 'not_needed';
  static const String qualityIssue = 'quality_issue';
  static const String sizeIssue = 'size_issue';
  static const String colorIssue = 'color_issue';
  static const String other = 'other';

  static const List<String> all = [
    defective,
    wrongItem,
    damaged,
    notNeeded,
    qualityIssue,
    sizeIssue,
    colorIssue,
    other,
  ];

  static String getDisplayName(String reason) {
    switch (reason) {
      case defective:
        return 'Defective Product';
      case wrongItem:
        return 'Wrong Item Received';
      case damaged:
        return 'Damaged in Transit';
      case notNeeded:
        return 'No Longer Needed';
      case qualityIssue:
        return 'Quality Issue';
      case sizeIssue:
        return 'Size Issue';
      case colorIssue:
        return 'Color Issue';
      case other:
        return 'Other';
      default:
        return reason;
    }
  }
}
