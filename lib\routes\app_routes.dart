/// Application routes for Al Ameen Sales App
class AppRoutes {
  // Prevent instantiation
  AppRoutes._();

  // Common routes
  static const String splash = '/splash';
  static const String login = '/login';

  // Retail routes
  static const String retailDashboard = '/retail/dashboard';
  static const String retailOrders = '/retail/orders';
  static const String retailOrderDetails = '/retail/orders/:id';
  static const String retailCreateOrder = '/retail/orders/create';
  static const String retailCustomers = '/retail/customers';
  static const String retailCustomerDetails = '/retail/customers/:id';
  static const String retailCreateCustomer = '/retail/customers/create';
  static const String retailVisits = '/retail/visits';
  static const String retailVisitDetails = '/retail/visits/:id';
  static const String retailScheduleVisit = '/retail/visits/schedule';
  static const String retailInvoices = '/retail/invoices';
  static const String retailInvoiceDetails = '/retail/invoices/:id';
  static const String retailCreateInvoice = '/retail/invoices/create';
  static const String retailReturns = '/retail/returns';
  static const String retailReturnDetails = '/retail/returns/:id';
  static const String retailCreateReturn = '/retail/returns/create';
  static const String retailNotifications = '/retail/notifications';
  static const String retailProfile = '/retail/profile';
  static const String retailSettings = '/retail/settings';

  // Wholesale routes
  static const String wholesaleDashboard = '/wholesale/dashboard';
  static const String wholesaleOrders = '/wholesale/orders';
  static const String wholesaleOrderDetails = '/wholesale/orders/:id';
  static const String wholesaleCreateOrder = '/wholesale/orders/create';
  static const String wholesaleCustomers = '/wholesale/customers';
  static const String wholesaleCustomerDetails = '/wholesale/customers/:id';
  static const String wholesaleCreateCustomer = '/wholesale/customers/create';
  static const String wholesaleVisits = '/wholesale/visits';
  static const String wholesaleVisitDetails = '/wholesale/visits/:id';
  static const String wholesaleScheduleVisit = '/wholesale/visits/schedule';
  static const String wholesaleNotifications = '/wholesale/notifications';

  // Profile and settings
  static const String profile = '/profile';
  static const String settings = '/settings';

  // Utility methods
  static String getRetailOrderDetails(String orderId) {
    return retailOrderDetails.replaceAll(':id', orderId);
  }

  static String getRetailCustomerDetails(String customerId) {
    return retailCustomerDetails.replaceAll(':id', customerId);
  }

  static String getRetailVisitDetails(String visitId) {
    return retailVisitDetails.replaceAll(':id', visitId);
  }

  static String getRetailInvoiceDetails(String invoiceId) {
    return retailInvoiceDetails.replaceAll(':id', invoiceId);
  }

  static String getRetailReturnDetails(String returnId) {
    return retailReturnDetails.replaceAll(':id', returnId);
  }

  static String getWholesaleOrderDetails(String orderId) {
    return wholesaleOrderDetails.replaceAll(':id', orderId);
  }

  static String getWholesaleCustomerDetails(String customerId) {
    return wholesaleCustomerDetails.replaceAll(':id', customerId);
  }

  static String getWholesaleVisitDetails(String visitId) {
    return wholesaleVisitDetails.replaceAll(':id', visitId);
  }
}
