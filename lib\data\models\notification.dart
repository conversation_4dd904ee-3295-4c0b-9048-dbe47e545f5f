import 'package:json_annotation/json_annotation.dart';

part 'notification.g.dart';

/// Notification model for Al Ameen Sales App
@JsonSerializable()
class Notification {
  final String id;
  final String title;
  final String message;
  final String type; // 'order', 'visit', 'invoice', 'return', 'general', 'system'
  final String? entityId; // ID of related order, visit, etc.
  final String? entityType; // 'order', 'visit', 'invoice', 'return'
  final String priority; // 'low', 'medium', 'high', 'urgent'
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final String? actionUrl;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? icon;

  const Notification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.entityId,
    this.entityType,
    this.priority = 'medium',
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.actionUrl,
    this.data,
    this.imageUrl,
    this.icon,
  });

  /// Check if notification is unread
  bool get isUnread => !isRead;

  /// Check if notification is high priority
  bool get isHighPriority => priority == 'high' || priority == 'urgent';

  /// Check if notification is urgent
  bool get isUrgent => priority == 'urgent';

  /// Check if notification is order related
  bool get isOrderNotification => type == 'order';

  /// Check if notification is visit related
  bool get isVisitNotification => type == 'visit';

  /// Check if notification is invoice related
  bool get isInvoiceNotification => type == 'invoice';

  /// Check if notification is return related
  bool get isReturnNotification => type == 'return';

  /// Check if notification is system notification
  bool get isSystemNotification => type == 'system';

  /// Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  factory Notification.fromJson(Map<String, dynamic> json) => _$NotificationFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationToJson(this);

  Notification copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? entityId,
    String? entityType,
    String? priority,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    String? actionUrl,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? icon,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      entityId: entityId ?? this.entityId,
      entityType: entityType ?? this.entityType,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      icon: icon ?? this.icon,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Notification && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Notification(id: $id, title: $title, type: $type, isRead: $isRead)';
}

/// Notification filters for searching and filtering
class NotificationFilters {
  final List<String>? types;
  final List<String>? priorities;
  final bool? isRead;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final String? entityType;
  final String? sortBy; // 'createdAt', 'priority', 'type'
  final String? sortOrder; // 'asc', 'desc'

  const NotificationFilters({
    this.types,
    this.priorities,
    this.isRead,
    this.createdAfter,
    this.createdBefore,
    this.entityType,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (types?.isNotEmpty == true) params['types'] = types!.join(',');
    if (priorities?.isNotEmpty == true) params['priorities'] = priorities!.join(',');
    if (isRead != null) params['is_read'] = isRead;
    if (createdAfter != null) params['created_after'] = createdAfter!.toIso8601String();
    if (createdBefore != null) params['created_before'] = createdBefore!.toIso8601String();
    if (entityType?.isNotEmpty == true) params['entity_type'] = entityType;
    if (sortBy?.isNotEmpty == true) params['sort_by'] = sortBy;
    if (sortOrder?.isNotEmpty == true) params['sort_order'] = sortOrder;
    
    return params;
  }
}

/// Notification types enum
class NotificationTypes {
  static const String order = 'order';
  static const String visit = 'visit';
  static const String invoice = 'invoice';
  static const String returnType = 'return';
  static const String general = 'general';
  static const String system = 'system';
  static const String reminder = 'reminder';
  static const String alert = 'alert';

  static const List<String> all = [
    order,
    visit,
    invoice,
    returnType,
    general,
    system,
    reminder,
    alert,
  ];

  static String getDisplayName(String type) {
    switch (type) {
      case order:
        return 'Order';
      case visit:
        return 'Visit';
      case invoice:
        return 'Invoice';
      case returnType:
        return 'Return';
      case general:
        return 'General';
      case system:
        return 'System';
      case reminder:
        return 'Reminder';
      case alert:
        return 'Alert';
      default:
        return type;
    }
  }
}

/// Notification priorities enum
class NotificationPriorities {
  static const String low = 'low';
  static const String medium = 'medium';
  static const String high = 'high';
  static const String urgent = 'urgent';

  static const List<String> all = [low, medium, high, urgent];

  static String getDisplayName(String priority) {
    switch (priority) {
      case low:
        return 'Low';
      case medium:
        return 'Medium';
      case high:
        return 'High';
      case urgent:
        return 'Urgent';
      default:
        return priority;
    }
  }
}

/// Notification summary for dashboard
@JsonSerializable()
class NotificationSummary {
  final int totalCount;
  final int unreadCount;
  final int orderCount;
  final int visitCount;
  final int invoiceCount;
  final int returnCount;
  final int urgentCount;
  final int highPriorityCount;

  const NotificationSummary({
    this.totalCount = 0,
    this.unreadCount = 0,
    this.orderCount = 0,
    this.visitCount = 0,
    this.invoiceCount = 0,
    this.returnCount = 0,
    this.urgentCount = 0,
    this.highPriorityCount = 0,
  });

  /// Check if there are unread notifications
  bool get hasUnread => unreadCount > 0;

  /// Check if there are urgent notifications
  bool get hasUrgent => urgentCount > 0;

  /// Check if there are high priority notifications
  bool get hasHighPriority => highPriorityCount > 0;

  factory NotificationSummary.fromJson(Map<String, dynamic> json) => _$NotificationSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationSummaryToJson(this);

  NotificationSummary copyWith({
    int? totalCount,
    int? unreadCount,
    int? orderCount,
    int? visitCount,
    int? invoiceCount,
    int? returnCount,
    int? urgentCount,
    int? highPriorityCount,
  }) {
    return NotificationSummary(
      totalCount: totalCount ?? this.totalCount,
      unreadCount: unreadCount ?? this.unreadCount,
      orderCount: orderCount ?? this.orderCount,
      visitCount: visitCount ?? this.visitCount,
      invoiceCount: invoiceCount ?? this.invoiceCount,
      returnCount: returnCount ?? this.returnCount,
      urgentCount: urgentCount ?? this.urgentCount,
      highPriorityCount: highPriorityCount ?? this.highPriorityCount,
    );
  }
}
