import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/themes/app_colors.dart';
import '../../core/themes/text_styles.dart';
import '../../core/utils/format_utils.dart';
import '../../data/models/order.dart';

/// Order card widget for displaying order information
class OrderCard extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            order.orderNumber,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            order.customerName,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Status chip
                    _buildStatusChip(),
                    
                    SizedBox(width: 8.w),
                    
                    // Actions menu
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18.sp),
                              SizedBox(width: 8.w),
                              Text('edit'.tr),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18.sp, color: AppColors.error),
                              SizedBox(width: 8.w),
                              Text('delete'.tr, style: TextStyle(color: AppColors.error)),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(
                        Icons.more_vert,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Order details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.calendar_today,
                        label: 'order_date'.tr,
                        value: FormatUtils.formatDate(order.orderDate),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildDetailItem(
                        icon: Icons.attach_money,
                        label: 'total_amount'.tr,
                        value: FormatUtils.formatCurrency(order.totalAmount),
                      ),
                    ),
                  ],
                ),
                
                if (order.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 12.h),
                  _buildDetailItem(
                    icon: Icons.note,
                    label: 'notes'.tr,
                    value: order.notes!,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    String statusText;

    switch (order.status) {
      case 'pending':
        statusColor = AppColors.warning;
        statusText = 'pending'.tr;
        break;
      case 'confirmed':
        statusColor = AppColors.info;
        statusText = 'confirmed'.tr;
        break;
      case 'processing':
        statusColor = AppColors.secondary;
        statusText = 'processing'.tr;
        break;
      case 'shipped':
        statusColor = AppColors.primary;
        statusText = 'shipped'.tr;
        break;
      case 'delivered':
        statusColor = AppColors.success;
        statusText = 'delivered'.tr;
        break;
      case 'cancelled':
        statusColor = AppColors.error;
        statusText = 'cancelled'.tr;
        break;
      default:
        statusColor = AppColors.grey500;
        statusText = order.status;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: AppTextStyles.labelSmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: AppColors.onSurfaceVariant,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
