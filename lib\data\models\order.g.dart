// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
  id: json['id'] as String,
  orderNumber: json['orderNumber'] as String,
  customerId: json['customerId'] as String,
  customerName: json['customerName'] as String,
  status: json['status'] as String,
  orderDate: DateTime.parse(json['orderDate'] as String),
  deliveryDate:
      json['deliveryDate'] == null
          ? null
          : DateTime.parse(json['deliveryDate'] as String),
  shippedDate:
      json['shippedDate'] == null
          ? null
          : DateTime.parse(json['shippedDate'] as String),
  items:
      (json['items'] as List<dynamic>)
          .map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  subtotal: (json['subtotal'] as num).toDouble(),
  taxAmount: (json['taxAmount'] as num).toDouble(),
  discountAmount: (json['discountAmount'] as num).toDouble(),
  shippingAmount: (json['shippingAmount'] as num).toDouble(),
  totalAmount: (json['totalAmount'] as num).toDouble(),
  paymentMethod: json['paymentMethod'] as String,
  paymentStatus: json['paymentStatus'] as String,
  notes: json['notes'] as String?,
  deliveryAddress: json['deliveryAddress'] as String?,
  createdBy: json['createdBy'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  cancelReason: json['cancelReason'] as String?,
  cancelledAt:
      json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
);

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
  'id': instance.id,
  'orderNumber': instance.orderNumber,
  'customerId': instance.customerId,
  'customerName': instance.customerName,
  'status': instance.status,
  'orderDate': instance.orderDate.toIso8601String(),
  'deliveryDate': instance.deliveryDate?.toIso8601String(),
  'shippedDate': instance.shippedDate?.toIso8601String(),
  'items': instance.items,
  'subtotal': instance.subtotal,
  'taxAmount': instance.taxAmount,
  'discountAmount': instance.discountAmount,
  'shippingAmount': instance.shippingAmount,
  'totalAmount': instance.totalAmount,
  'paymentMethod': instance.paymentMethod,
  'paymentStatus': instance.paymentStatus,
  'notes': instance.notes,
  'deliveryAddress': instance.deliveryAddress,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'cancelReason': instance.cancelReason,
  'cancelledAt': instance.cancelledAt?.toIso8601String(),
};

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => OrderItem(
  id: json['id'] as String,
  productId: json['productId'] as String,
  productName: json['productName'] as String,
  productCode: json['productCode'] as String?,
  productImage: json['productImage'] as String?,
  quantity: (json['quantity'] as num).toInt(),
  unitPrice: (json['unitPrice'] as num).toDouble(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  discountAmount: (json['discountAmount'] as num?)?.toDouble(),
  taxAmount: (json['taxAmount'] as num?)?.toDouble(),
  notes: json['notes'] as String?,
  unit: json['unit'] as String? ?? 'pcs',
);

Map<String, dynamic> _$OrderItemToJson(OrderItem instance) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'productName': instance.productName,
  'productCode': instance.productCode,
  'productImage': instance.productImage,
  'quantity': instance.quantity,
  'unitPrice': instance.unitPrice,
  'totalPrice': instance.totalPrice,
  'discountAmount': instance.discountAmount,
  'taxAmount': instance.taxAmount,
  'notes': instance.notes,
  'unit': instance.unit,
};
