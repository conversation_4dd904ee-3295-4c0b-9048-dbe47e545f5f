/// Base exception class for Al Ameen Sales App
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  /// Get user-friendly error message
  String get userFriendlyMessage => message;

  @override
  String toString() => 'AppException: $message';
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});

  @override
  String toString() => 'NetworkException: $message';
}

/// API related exceptions
class ApiException extends AppException {
  final int? statusCode;

  const ApiException(
    super.message, {
    this.statusCode,
    super.code,
    super.details,
  });

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.details});

  @override
  String toString() => 'AuthException: $message';
}

/// Validation related exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException(
    super.message, {
    this.fieldErrors,
    super.code,
    super.details,
  });

  @override
  String toString() => 'ValidationException: $message';
}

/// Storage related exceptions
class StorageException extends AppException {
  const StorageException(super.message, {super.code, super.details});

  @override
  String toString() => 'StorageException: $message';
}

/// Permission related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});

  @override
  String toString() => 'PermissionException: $message';
}

/// File operation related exceptions
class FileException extends AppException {
  const FileException(super.message, {super.code, super.details});

  @override
  String toString() => 'FileException: $message';
}

/// Database related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code, super.details});

  @override
  String toString() => 'DatabaseException: $message';
}

/// Cache related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.details});

  @override
  String toString() => 'CacheException: $message';
}

/// Business logic related exceptions
class BusinessException extends AppException {
  const BusinessException(super.message, {super.code, super.details});

  @override
  String toString() => 'BusinessException: $message';
}

/// Timeout related exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code, super.details});

  @override
  String toString() => 'TimeoutException: $message';
}

/// Server related exceptions
class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.details});

  @override
  String toString() => 'ServerException: $message';
}

/// Client related exceptions
class ClientException extends AppException {
  const ClientException(super.message, {super.code, super.details});

  @override
  String toString() => 'ClientException: $message';
}

/// Parsing related exceptions
class ParseException extends AppException {
  const ParseException(super.message, {super.code, super.details});

  @override
  String toString() => 'ParseException: $message';
}

/// Configuration related exceptions
class ConfigException extends AppException {
  const ConfigException(super.message, {super.code, super.details});

  @override
  String toString() => 'ConfigException: $message';
}

/// Feature not available exceptions
class FeatureNotAvailableException extends AppException {
  const FeatureNotAvailableException(super.message, {super.code, super.details});

  @override
  String toString() => 'FeatureNotAvailableException: $message';
}

/// Resource not found exceptions
class NotFoundException extends AppException {
  const NotFoundException(super.message, {super.code, super.details});

  @override
  String toString() => 'NotFoundException: $message';
}

/// Conflict exceptions (e.g., duplicate data)
class ConflictException extends AppException {
  const ConflictException(super.message, {super.code, super.details});

  @override
  String toString() => 'ConflictException: $message';
}

/// Rate limit exceeded exceptions
class RateLimitException extends AppException {
  final DateTime? retryAfter;

  const RateLimitException(
    super.message, {
    this.retryAfter,
    super.code,
    super.details,
  });

  @override
  String toString() => 'RateLimitException: $message';
}

/// Maintenance mode exceptions
class MaintenanceException extends AppException {
  final DateTime? estimatedEndTime;

  const MaintenanceException(
    super.message, {
    this.estimatedEndTime,
    super.code,
    super.details,
  });

  @override
  String toString() => 'MaintenanceException: $message';
}

/// Specific API error codes
class ApiErrorCodes {
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String tokenExpired = 'TOKEN_EXPIRED';
  static const String tokenInvalid = 'TOKEN_INVALID';
  static const String userNotFound = 'USER_NOT_FOUND';
  static const String userBlocked = 'USER_BLOCKED';
  static const String validationFailed = 'VALIDATION_FAILED';
  static const String resourceNotFound = 'RESOURCE_NOT_FOUND';
  static const String duplicateResource = 'DUPLICATE_RESOURCE';
  static const String insufficientPermissions = 'INSUFFICIENT_PERMISSIONS';
  static const String rateLimitExceeded = 'RATE_LIMIT_EXCEEDED';
  static const String serverError = 'SERVER_ERROR';
  static const String maintenanceMode = 'MAINTENANCE_MODE';
  static const String networkError = 'NETWORK_ERROR';
  static const String timeoutError = 'TIMEOUT_ERROR';
  static const String unknownError = 'UNKNOWN_ERROR';
}

/// Exception factory for creating specific exceptions based on error codes
class ExceptionFactory {
  static AppException createFromApiError({
    required String message,
    String? code,
    int? statusCode,
    dynamic details,
  }) {
    switch (code) {
      case ApiErrorCodes.invalidCredentials:
      case ApiErrorCodes.tokenExpired:
      case ApiErrorCodes.tokenInvalid:
      case ApiErrorCodes.userNotFound:
      case ApiErrorCodes.userBlocked:
        return AuthException(message, code: code, details: details);

      case ApiErrorCodes.validationFailed:
        return ValidationException(message, code: code, details: details);

      case ApiErrorCodes.resourceNotFound:
        return NotFoundException(message, code: code, details: details);

      case ApiErrorCodes.duplicateResource:
        return ConflictException(message, code: code, details: details);

      case ApiErrorCodes.insufficientPermissions:
        return PermissionException(message, code: code, details: details);

      case ApiErrorCodes.rateLimitExceeded:
        return RateLimitException(message, code: code, details: details);

      case ApiErrorCodes.maintenanceMode:
        return MaintenanceException(message, code: code, details: details);

      case ApiErrorCodes.networkError:
        return NetworkException(message, code: code, details: details);

      case ApiErrorCodes.timeoutError:
        return TimeoutException(message, code: code, details: details);

      case ApiErrorCodes.serverError:
        return ServerException(message, code: code, details: details);

      default:
        return ApiException(
          message,
          statusCode: statusCode,
          code: code,
          details: details,
        );
    }
  }

  static AppException createFromHttpStatus({
    required int statusCode,
    required String message,
    String? code,
    dynamic details,
  }) {
    switch (statusCode) {
      case 400:
        return ValidationException(message, code: code, details: details);
      case 401:
        return AuthException(message, code: code, details: details);
      case 403:
        return PermissionException(message, code: code, details: details);
      case 404:
        return NotFoundException(message, code: code, details: details);
      case 409:
        return ConflictException(message, code: code, details: details);
      case 429:
        return RateLimitException(message, code: code, details: details);
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerException(message, code: code, details: details);
      default:
        return ApiException(
          message,
          statusCode: statusCode,
          code: code,
          details: details,
        );
    }
  }
}
