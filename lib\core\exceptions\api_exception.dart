import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'app_exception.dart';

/// API-specific exception handling utilities
class ApiExceptionHandler {
  /// Handle HTTP response and convert to appropriate exception
  static AppException handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final message = _getErrorMessage(response);
    
    return ExceptionFactory.createFromHttpStatus(
      statusCode: statusCode,
      message: message,
      details: response.body,
    );
  }

  /// Handle HTTP client errors
  static AppException handleHttpError(dynamic error) {
    if (error is SocketException) {
      return const NetworkException(
        'No internet connection. Please check your network settings.',
        code: ApiErrorCodes.networkError,
      );
    }
    
    if (error is HttpException) {
      return NetworkException(
        'Network error: ${error.message}',
        code: ApiErrorCodes.networkError,
        details: error,
      );
    }
    
    if (error is FormatException) {
      return ParseException(
        'Invalid response format from server',
        code: 'INVALID_FORMAT',
        details: error,
      );
    }
    
    return ApiException(
      'Unexpected error occurred: ${error.toString()}',
      code: ApiErrorCodes.unknownError,
      details: error,
    );
  }

  /// Handle timeout errors
  static AppException handleTimeout() {
    return const TimeoutException(
      'Request timed out. Please try again.',
      code: ApiErrorCodes.timeoutError,
    );
  }

  /// Extract error message from HTTP response
  static String _getErrorMessage(http.Response response) {
    try {
      // Try to parse JSON response for error message
      final body = response.body;
      if (body.isNotEmpty) {
        // This is a simplified version - in real implementation,
        // you would parse JSON and extract the error message
        return 'Server error: ${response.statusCode}';
      }
    } catch (e) {
      // If parsing fails, use default message
    }
    
    return _getDefaultErrorMessage(response.statusCode);
  }

  /// Get default error message based on status code
  static String _getDefaultErrorMessage(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Conflict. The resource already exists.';
      case 422:
        return 'Validation failed. Please check your input.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
        return 'Bad gateway. Server is temporarily unavailable.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  /// Check if error is retryable
  static bool isRetryable(AppException exception) {
    if (exception is NetworkException) return true;
    if (exception is TimeoutException) return true;
    if (exception is ServerException) return true;
    
    if (exception is ApiException) {
      final statusCode = exception.statusCode;
      if (statusCode == null) return false;
      
      // Retry on server errors and some client errors
      return statusCode >= 500 || statusCode == 408 || statusCode == 429;
    }
    
    return false;
  }

  /// Get retry delay based on exception type
  static Duration getRetryDelay(AppException exception, int attemptNumber) {
    if (exception is RateLimitException) {
      // If server provides retry-after, use it
      if (exception.retryAfter != null) {
        return exception.retryAfter!.difference(DateTime.now());
      }
      // Otherwise use exponential backoff
      return Duration(seconds: (attemptNumber * 2).clamp(1, 60));
    }
    
    // Exponential backoff for other retryable errors
    return Duration(seconds: (attemptNumber * attemptNumber).clamp(1, 30));
  }

  /// Check if exception requires user authentication
  static bool requiresAuthentication(AppException exception) {
    if (exception is AuthException) return true;
    
    if (exception is ApiException) {
      return exception.statusCode == 401;
    }
    
    return false;
  }

  /// Check if exception indicates maintenance mode
  static bool isMaintenanceMode(AppException exception) {
    if (exception is MaintenanceException) return true;
    
    if (exception is ApiException) {
      return exception.statusCode == 503 && 
             exception.code == ApiErrorCodes.maintenanceMode;
    }
    
    return false;
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(AppException exception) {
    if (exception is NetworkException) {
      return 'Please check your internet connection and try again.';
    }
    
    if (exception is AuthException) {
      return 'Your session has expired. Please login again.';
    }
    
    if (exception is ValidationException) {
      return 'Please check your input and try again.';
    }
    
    if (exception is PermissionException) {
      return 'You don\'t have permission to perform this action.';
    }
    
    if (exception is NotFoundException) {
      return 'The requested information could not be found.';
    }
    
    if (exception is ConflictException) {
      return 'This information already exists. Please try with different details.';
    }
    
    if (exception is RateLimitException) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    
    if (exception is MaintenanceException) {
      return 'The service is temporarily unavailable for maintenance. Please try again later.';
    }
    
    if (exception is TimeoutException) {
      return 'The request took too long. Please try again.';
    }
    
    if (exception is ServerException) {
      return 'A server error occurred. Please try again later.';
    }
    
    // Return the original message for other exceptions
    return exception.message;
  }

  /// Log exception for debugging
  static void logException(AppException exception, {String? context}) {
    // In a real app, you would use a proper logging framework
    // For now, we'll use debugPrint which is safe for production
    // ignore: avoid_print
    debugPrint('Exception in ${context ?? 'Unknown'}: ${exception.toString()}');
    if (exception.details != null) {
      // ignore: avoid_print
      debugPrint('Details: ${exception.details}');
    }
  }
}

/// Extension methods for easier exception handling
extension AppExceptionExtension on AppException {
  /// Check if this exception is retryable
  bool get isRetryable => ApiExceptionHandler.isRetryable(this);
  
  /// Check if this exception requires authentication
  bool get requiresAuth => ApiExceptionHandler.requiresAuthentication(this);
  
  /// Check if this exception indicates maintenance mode
  bool get isMaintenanceMode => ApiExceptionHandler.isMaintenanceMode(this);
  
  /// Get user-friendly error message
  String get userFriendlyMessage => ApiExceptionHandler.getUserFriendlyMessage(this);
  
  /// Get retry delay for this exception
  Duration getRetryDelay(int attemptNumber) => 
      ApiExceptionHandler.getRetryDelay(this, attemptNumber);
  
  /// Log this exception
  void log({String? context}) => 
      ApiExceptionHandler.logException(this, context: context);
}
