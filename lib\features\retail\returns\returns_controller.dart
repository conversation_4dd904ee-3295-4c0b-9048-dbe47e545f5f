import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/return.dart';
import '../../../core/utils/helpers.dart';

/// Controller for returns management
class ReturnsController extends GetxController {
  // Observable variables
  final isLoading = false.obs;
  final returns = <Return>[].obs;
  final filteredReturns = <Return>[].obs;
  final searchQuery = ''.obs;
  final selectedStatus = 'all'.obs;
  final selectedReason = 'all'.obs;
  final selectedDateRange = 'all'.obs;

  // Filter options
  final statusOptions = [
    'all',
    'requested',
    'approved',
    'rejected',
    'processing',
    'completed'
  ].obs;

  final reasonOptions = [
    'all',
    'defective',
    'wrong_item',
    'damaged',
    'not_needed',
    'quality_issue',
    'size_issue',
    'color_issue',
    'other'
  ].obs;

  final dateRangeOptions = [
    'all',
    'today',
    'this_week',
    'this_month',
    'last_month'
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadReturns();
    
    // Listen to search and filter changes
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedStatus, (_) => _applyFilters());
    ever(selectedReason, (_) => _applyFilters());
    ever(selectedDateRange, (_) => _applyFilters());
  }

  /// Load returns from service
  Future<void> loadReturns() async {
    try {
      isLoading.value = true;
      
      // Mock data for demonstration
      returns.value = _generateMockReturns();
      
      // TODO: Replace with actual API call
      // returns.value = await _returnService.getReturns();
      
      _applyFilters();
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_load_returns'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply search and filters
  void _applyFilters() {
    var filtered = returns.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((returnItem) {
        return returnItem.returnNumber.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               returnItem.customerName.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               (returnItem.invoiceId?.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != 'all') {
      filtered = filtered.where((returnItem) => returnItem.status == selectedStatus.value).toList();
    }

    // Apply reason filter
    if (selectedReason.value != 'all') {
      filtered = filtered.where((returnItem) => returnItem.reason == selectedReason.value).toList();
    }

    // Apply date range filter
    if (selectedDateRange.value != 'all') {
      final now = DateTime.now();
      DateTime? startDate;

      switch (selectedDateRange.value) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case 'this_week':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          break;
        case 'this_month':
          startDate = DateTime(now.year, now.month, 1);
          break;
        case 'last_month':
          final lastMonth = DateTime(now.year, now.month - 1, 1);
          startDate = lastMonth;
          break;
      }

      if (startDate != null) {
        filtered = filtered.where((returnItem) => returnItem.requestDate.isAfter(startDate!)).toList();
      }
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.requestDate.compareTo(a.requestDate));

    filteredReturns.value = filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Update status filter
  void updateStatusFilter(String status) {
    selectedStatus.value = status;
  }

  /// Update reason filter
  void updateReasonFilter(String reason) {
    selectedReason.value = reason;
  }

  /// Update date range filter
  void updateDateRangeFilter(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
    selectedReason.value = 'all';
    selectedDateRange.value = 'all';
  }

  /// Navigate to return details
  void navigateToReturnDetails(String returnId) {
    Get.toNamed('/retail/returns/$returnId');
  }

  /// Navigate to create return
  void navigateToCreateReturn() {
    Get.toNamed('/retail/returns/create');
  }

  /// Navigate to edit return
  void navigateToEditReturn(String returnId) {
    Get.toNamed('/retail/returns/$returnId/edit');
  }

  /// Approve return
  Future<void> approveReturn(String returnId, String notes) async {
    try {
      // TODO: Call API to approve return
      // await _returnService.approveReturn(returnId, notes);
      
      // Update local list
      final returnIndex = returns.indexWhere((returnItem) => returnItem.id == returnId);
      if (returnIndex != -1) {
        returns[returnIndex] = returns[returnIndex].copyWith(
          status: 'approved',
          approvedBy: 'USER-001', // TODO: Get from auth service
          approvedDate: DateTime.now(),
          notes: notes,
        );
        _applyFilters();
      }
      
      Helpers.showSuccessSnackbar('return_approved_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_approve_return'.tr);
    }
  }

  /// Reject return
  Future<void> rejectReturn(String returnId, String reason) async {
    try {
      // TODO: Call API to reject return
      // await _returnService.rejectReturn(returnId, reason);
      
      // Update local list
      final returnIndex = returns.indexWhere((returnItem) => returnItem.id == returnId);
      if (returnIndex != -1) {
        returns[returnIndex] = returns[returnIndex].copyWith(
          status: 'rejected',
          rejectionReason: reason,
        );
        _applyFilters();
      }
      
      Helpers.showSuccessSnackbar('return_rejected_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_reject_return'.tr);
    }
  }

  /// Process return (mark as processed)
  Future<void> processReturn(String returnId) async {
    try {
      // TODO: Call API to process return
      // await _returnService.processReturn(returnId);
      
      // Update local list
      final returnIndex = returns.indexWhere((returnItem) => returnItem.id == returnId);
      if (returnIndex != -1) {
        returns[returnIndex] = returns[returnIndex].copyWith(
          status: 'processing',
          processedDate: DateTime.now(),
        );
        _applyFilters();
      }
      
      Helpers.showSuccessSnackbar('return_processed_successfully'.tr);
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_process_return'.tr);
    }
  }

  /// Cancel return
  Future<void> cancelReturn(String returnId, String reason) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('confirm_cancel'.tr),
          content: Text('confirm_cancel_return'.tr),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('no'.tr),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('yes'.tr),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Call API to cancel return
        // await _returnService.cancelReturn(returnId, reason);
        
        // Update local list
        final returnIndex = returns.indexWhere((returnItem) => returnItem.id == returnId);
        if (returnIndex != -1) {
          returns[returnIndex] = returns[returnIndex].copyWith(
            status: 'cancelled',
            notes: reason,
          );
          _applyFilters();
        }
        
        Helpers.showSuccessSnackbar('return_cancelled_successfully'.tr);
      }
    } catch (e) {
      Helpers.showErrorSnackbar('failed_to_cancel_return'.tr);
    }
  }

  /// Refresh returns
  Future<void> refreshReturns() async {
    await loadReturns();
  }

  /// Get total return amount for filtered returns
  double get totalReturnAmount {
    return filteredReturns.fold(0.0, (sum, returnItem) => sum + returnItem.totalAmount);
  }

  /// Get approved return amount for filtered returns
  double get approvedReturnAmount {
    return filteredReturns
        .where((returnItem) => returnItem.status == 'approved' || returnItem.status == 'processing')
        .fold(0.0, (sum, returnItem) => sum + returnItem.totalAmount);
  }

  /// Get pending return amount for filtered returns
  double get pendingReturnAmount {
    return filteredReturns
        .where((returnItem) => returnItem.status == 'requested')
        .fold(0.0, (sum, returnItem) => sum + returnItem.totalAmount);
  }

  /// Generate mock returns for demonstration
  List<Return> _generateMockReturns() {
    final now = DateTime.now();
    return [
      Return(
        id: '1',
        returnNumber: '001',
        orderId: 'ORD-001',
        invoiceId: 'INV-001',
        customerId: 'CUST-001',
        customerName: 'محمد أحمد التجاري',
        requestDate: now.subtract(const Duration(hours: 2)),
        status: 'requested',
        reason: 'damaged',
        totalAmount: 250.0,
        refundAmount: 250.0,
        refundMethod: 'credit',
        items: [],
        notes: 'المنتج وصل تالف',
        requestedBy: 'USER-001',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 1)),
      ),
      Return(
        id: '2',
        returnNumber: '002',
        orderId: 'ORD-002',
        invoiceId: 'INV-002',
        customerId: 'CUST-002',
        customerName: 'فاطمة علي للتجارة',
        requestDate: now.subtract(const Duration(days: 1)),
        status: 'approved',
        reason: 'wrong_item',
        totalAmount: 500.0,
        refundAmount: 500.0,
        refundMethod: 'cash',
        items: [],
        notes: 'تم إرسال منتج خاطئ',
        approvedBy: 'USER-002',
        approvedDate: now.subtract(const Duration(hours: 12)),
        requestedBy: 'USER-001',
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(hours: 12)),
      ),
      Return(
        id: '3',
        returnNumber: '003',
        orderId: 'ORD-003',
        invoiceId: 'INV-003',
        customerId: 'CUST-003',
        customerName: 'عبدالله محمد المؤسسة',
        requestDate: now.subtract(const Duration(days: 3)),
        status: 'rejected',
        reason: 'not_needed',
        totalAmount: 150.0,
        refundAmount: 0.0,
        refundMethod: 'credit',
        items: [],
        notes: 'العميل غير راضي عن المنتج',
        rejectionReason: 'لا يمكن إرجاع هذا المنتج حسب السياسة',
        requestedBy: 'USER-001',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
    ];
  }
}
