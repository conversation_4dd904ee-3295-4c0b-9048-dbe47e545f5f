import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';
import 'splash_controller.dart';
import '../../../core/themes/app_colors.dart';
import '../../../core/themes/text_styles.dart';

/// Splash screen for Al Ameen Sales App
class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, // Force RTL for Arabic
      child: Scaffold(
        backgroundColor: AppColors.primary,
        body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: Safe<PERSON>rea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo section
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App logo
                    Container(
                      width: 120.w,
                      height: 120.w,
                      decoration: BoxDecoration(
                        color: AppColors.onPrimary,
                        borderRadius: BorderRadius.circular(24.r),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowDark,
                            blurRadius: 20.r,
                            offset: Offset(0, 8.h),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.business_center,
                        size: 60.sp,
                        color: AppColors.primary,
                      ),
                    )
                        .animate()
                        .scale(
                          duration: 800.ms,
                          curve: Curves.elasticOut,
                        )
                        .fadeIn(duration: 600.ms),
                    
                    SizedBox(height: 24.h),
                    
                    // App name
                    Text(
                      'app_arabic_name'.tr,
                      style: AppTextStyles.headlineLarge.copyWith(
                        color: AppColors.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                        .animate(delay: 300.ms)
                        .fadeIn(duration: 600.ms)
                        .slideY(begin: 0.3, end: 0),

                    SizedBox(height: 8.h),

                    // App tagline
                    Text(
                      'app_tagline'.tr,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.onPrimary.withValues(alpha: 0.9),
                      ),
                    )
                        .animate(delay: 500.ms)
                        .fadeIn(duration: 600.ms)
                        .slideY(begin: 0.3, end: 0),
                  ],
                ),
              ),
              
              // Loading section
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Loading indicator
                    SizedBox(
                      width: 40.w,
                      height: 40.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 3.w,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.onPrimary,
                        ),
                      ),
                    )
                        .animate(delay: 800.ms)
                        .fadeIn(duration: 600.ms)
                        .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
                    
                    SizedBox(height: 16.h),
                    
                    // Loading text
                    Text(
                      'Loading...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.onPrimary.withValues(alpha: 0.8),
                      ),
                    )
                        .animate(delay: 1000.ms)
                        .fadeIn(duration: 600.ms),
                  ],
                ),
              ),
              
              // Footer section
              Padding(
                padding: EdgeInsets.only(bottom: 32.h),
                child: Column(
                  children: [
                    Text(
                      'Version 1.0.0',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.onPrimary.withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '© 2024 Al Ameen Sales',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.onPrimary.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              )
                  .animate(delay: 1200.ms)
                  .fadeIn(duration: 600.ms)
                  .slideY(begin: 0.3, end: 0),
            ],
          ),
        ),
      ),
      ),
    );
  }
}
